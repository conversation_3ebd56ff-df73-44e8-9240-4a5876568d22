{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 27554, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 27554, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 27554, "tid": 12988, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 27554, "tid": 12988, "ts": 1748551706718822, "dur": 743, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 27554, "tid": 12988, "ts": 1748551706726079, "dur": 972, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 27554, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 27554, "tid": 1, "ts": 1748551705538950, "dur": 50053, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748551705589011, "dur": 154993, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748551705744014, "dur": 60146, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 27554, "tid": 12988, "ts": 1748551706727064, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 27554, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705536278, "dur": 141857, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705678139, "dur": 1029363, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705679141, "dur": 8143, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705687311, "dur": 929, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705688241, "dur": 5212, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705693486, "dur": 796, "ph": "X", "name": "ProcessMessages 6369", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705694306, "dur": 3013, "ph": "X", "name": "ReadAsync 6369", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705697323, "dur": 5, "ph": "X", "name": "ProcessMessages 8177", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705697329, "dur": 1473, "ph": "X", "name": "ReadAsync 8177", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705698863, "dur": 24, "ph": "X", "name": "ProcessMessages 8162", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705698889, "dur": 336, "ph": "X", "name": "ReadAsync 8162", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705699265, "dur": 4, "ph": "X", "name": "ProcessMessages 8148", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705699270, "dur": 399, "ph": "X", "name": "ReadAsync 8148", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705700056, "dur": 108, "ph": "X", "name": "ProcessMessages 1834", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705700166, "dur": 9338, "ph": "X", "name": "ReadAsync 1834", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705710229, "dur": 49, "ph": "X", "name": "ProcessMessages 6673", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705746843, "dur": 167, "ph": "X", "name": "ReadAsync 6673", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747011, "dur": 5, "ph": "X", "name": "ProcessMessages 8185", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747017, "dur": 69, "ph": "X", "name": "ReadAsync 8185", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747089, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747091, "dur": 361, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747521, "dur": 5, "ph": "X", "name": "ProcessMessages 1675", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747529, "dur": 111, "ph": "X", "name": "ReadAsync 1675", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747641, "dur": 7, "ph": "X", "name": "ProcessMessages 1516", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747650, "dur": 37, "ph": "X", "name": "ReadAsync 1516", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747690, "dur": 54, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747746, "dur": 154, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747903, "dur": 4, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747910, "dur": 86, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705747999, "dur": 3, "ph": "X", "name": "ProcessMessages 1729", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748020, "dur": 68, "ph": "X", "name": "ReadAsync 1729", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748091, "dur": 4, "ph": "X", "name": "ProcessMessages 2028", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748097, "dur": 92, "ph": "X", "name": "ReadAsync 2028", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748191, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748193, "dur": 128, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748334, "dur": 1, "ph": "X", "name": "ProcessMessages 1313", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748336, "dur": 42, "ph": "X", "name": "ReadAsync 1313", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748380, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748406, "dur": 508, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748923, "dur": 4, "ph": "X", "name": "ProcessMessages 8100", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705748932, "dur": 137, "ph": "X", "name": "ReadAsync 8100", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749277, "dur": 4, "ph": "X", "name": "ProcessMessages 1596", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749283, "dur": 75, "ph": "X", "name": "ReadAsync 1596", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749361, "dur": 8, "ph": "X", "name": "ProcessMessages 4324", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749371, "dur": 115, "ph": "X", "name": "ReadAsync 4324", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749489, "dur": 4, "ph": "X", "name": "ProcessMessages 2240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749494, "dur": 130, "ph": "X", "name": "ReadAsync 2240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749632, "dur": 1, "ph": "X", "name": "ProcessMessages 1892", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749635, "dur": 36, "ph": "X", "name": "ReadAsync 1892", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749672, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749673, "dur": 69, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749745, "dur": 18, "ph": "X", "name": "ProcessMessages 1304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749765, "dur": 91, "ph": "X", "name": "ReadAsync 1304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749859, "dur": 4, "ph": "X", "name": "ProcessMessages 2170", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749864, "dur": 74, "ph": "X", "name": "ReadAsync 2170", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749941, "dur": 4, "ph": "X", "name": "ProcessMessages 2340", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705749946, "dur": 53, "ph": "X", "name": "ReadAsync 2340", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750003, "dur": 3, "ph": "X", "name": "ProcessMessages 1505", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750007, "dur": 63, "ph": "X", "name": "ReadAsync 1505", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750072, "dur": 2, "ph": "X", "name": "ProcessMessages 1199", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750076, "dur": 69, "ph": "X", "name": "ReadAsync 1199", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750147, "dur": 3, "ph": "X", "name": "ProcessMessages 2056", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750151, "dur": 95, "ph": "X", "name": "ReadAsync 2056", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750249, "dur": 3, "ph": "X", "name": "ProcessMessages 2380", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750263, "dur": 69, "ph": "X", "name": "ReadAsync 2380", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750336, "dur": 3, "ph": "X", "name": "ProcessMessages 2124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750342, "dur": 77, "ph": "X", "name": "ReadAsync 2124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750421, "dur": 3, "ph": "X", "name": "ProcessMessages 1888", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750425, "dur": 69, "ph": "X", "name": "ReadAsync 1888", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750497, "dur": 2, "ph": "X", "name": "ProcessMessages 1576", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750500, "dur": 66, "ph": "X", "name": "ReadAsync 1576", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750568, "dur": 40, "ph": "X", "name": "ProcessMessages 1488", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750611, "dur": 63, "ph": "X", "name": "ReadAsync 1488", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750677, "dur": 3, "ph": "X", "name": "ProcessMessages 2333", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750682, "dur": 75, "ph": "X", "name": "ReadAsync 2333", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750759, "dur": 2, "ph": "X", "name": "ProcessMessages 1380", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750763, "dur": 70, "ph": "X", "name": "ReadAsync 1380", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750855, "dur": 3, "ph": "X", "name": "ProcessMessages 1475", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750859, "dur": 55, "ph": "X", "name": "ReadAsync 1475", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750916, "dur": 4, "ph": "X", "name": "ProcessMessages 2054", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750922, "dur": 74, "ph": "X", "name": "ReadAsync 2054", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705750999, "dur": 13, "ph": "X", "name": "ProcessMessages 1421", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705751014, "dur": 63, "ph": "X", "name": "ReadAsync 1421", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705751079, "dur": 3, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705751083, "dur": 1479, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705752563, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705752569, "dur": 389, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705752961, "dur": 3, "ph": "X", "name": "ProcessMessages 1638", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705752967, "dur": 116, "ph": "X", "name": "ReadAsync 1638", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705753084, "dur": 3, "ph": "X", "name": "ProcessMessages 6099", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705753088, "dur": 50, "ph": "X", "name": "ReadAsync 6099", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705753139, "dur": 1, "ph": "X", "name": "ProcessMessages 2341", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705753141, "dur": 667, "ph": "X", "name": "ReadAsync 2341", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705753809, "dur": 3, "ph": "X", "name": "ProcessMessages 5519", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705753845, "dur": 130, "ph": "X", "name": "ReadAsync 5519", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705753976, "dur": 2, "ph": "X", "name": "ProcessMessages 4780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754368, "dur": 36, "ph": "X", "name": "ReadAsync 4780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754410, "dur": 3, "ph": "X", "name": "ProcessMessages 8181", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754414, "dur": 40, "ph": "X", "name": "ReadAsync 8181", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754457, "dur": 162, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754620, "dur": 2, "ph": "X", "name": "ProcessMessages 4544", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754623, "dur": 32, "ph": "X", "name": "ReadAsync 4544", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754671, "dur": 43, "ph": "X", "name": "ReadAsync 1417", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754716, "dur": 124, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754843, "dur": 149, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705754995, "dur": 37, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755034, "dur": 45, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755080, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755082, "dur": 50, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755133, "dur": 1, "ph": "X", "name": "ProcessMessages 1298", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755135, "dur": 45, "ph": "X", "name": "ReadAsync 1298", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755187, "dur": 98, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755287, "dur": 1, "ph": "X", "name": "ProcessMessages 1474", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755288, "dur": 40, "ph": "X", "name": "ReadAsync 1474", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755331, "dur": 182, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755516, "dur": 22, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755539, "dur": 247, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755789, "dur": 147, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705755942, "dur": 443, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705756388, "dur": 876, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705757266, "dur": 84, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705757358, "dur": 546, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705757907, "dur": 264, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705758172, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705758174, "dur": 584, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705758761, "dur": 276, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705759040, "dur": 454, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705759496, "dur": 588, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705760086, "dur": 111, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705760200, "dur": 563, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705760766, "dur": 465, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705761233, "dur": 1313, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705762547, "dur": 1, "ph": "X", "name": "ProcessMessages 2788", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705762549, "dur": 25, "ph": "X", "name": "ReadAsync 2788", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705762587, "dur": 570, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705763159, "dur": 150, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705763311, "dur": 2158, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705765472, "dur": 784, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705766261, "dur": 3, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705766295, "dur": 223, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705766520, "dur": 2, "ph": "X", "name": "ProcessMessages 1996", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705766523, "dur": 272, "ph": "X", "name": "ReadAsync 1996", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705766797, "dur": 2, "ph": "X", "name": "ProcessMessages 2517", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705766799, "dur": 322, "ph": "X", "name": "ReadAsync 2517", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705767126, "dur": 197, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705767326, "dur": 313, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705767641, "dur": 1, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705767650, "dur": 130, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705767783, "dur": 300, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705768084, "dur": 2, "ph": "X", "name": "ProcessMessages 3008", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705768087, "dur": 642, "ph": "X", "name": "ReadAsync 3008", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705768732, "dur": 5, "ph": "X", "name": "ProcessMessages 2511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705768739, "dur": 82, "ph": "X", "name": "ReadAsync 2511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705768823, "dur": 2, "ph": "X", "name": "ProcessMessages 1866", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705768825, "dur": 268, "ph": "X", "name": "ReadAsync 1866", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705769115, "dur": 2, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705769119, "dur": 128, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705769248, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705769250, "dur": 221, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705769473, "dur": 279, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705769753, "dur": 1, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705769755, "dur": 404, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705770160, "dur": 1, "ph": "X", "name": "ProcessMessages 1411", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705770162, "dur": 275, "ph": "X", "name": "ReadAsync 1411", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705770441, "dur": 247, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705770690, "dur": 192, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705770885, "dur": 277, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705771164, "dur": 422, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705771590, "dur": 14, "ph": "X", "name": "ProcessMessages 1893", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705771606, "dur": 401, "ph": "X", "name": "ReadAsync 1893", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705772009, "dur": 5, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705772015, "dur": 242, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705772260, "dur": 382, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705772646, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705772654, "dur": 33, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705772690, "dur": 893, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705773584, "dur": 1, "ph": "X", "name": "ProcessMessages 1178", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705773586, "dur": 29, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705773616, "dur": 1, "ph": "X", "name": "ProcessMessages 2455", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705773618, "dur": 360, "ph": "X", "name": "ReadAsync 2455", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705773979, "dur": 2, "ph": "X", "name": "ProcessMessages 2546", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705773982, "dur": 370, "ph": "X", "name": "ReadAsync 2546", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705774355, "dur": 94, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705774455, "dur": 270, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705774728, "dur": 605, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776067, "dur": 34, "ph": "X", "name": "ProcessMessages 5252", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776103, "dur": 67, "ph": "X", "name": "ReadAsync 5252", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776172, "dur": 3, "ph": "X", "name": "ProcessMessages 3763", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776176, "dur": 546, "ph": "X", "name": "ReadAsync 3763", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776725, "dur": 6, "ph": "X", "name": "ProcessMessages 3382", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776733, "dur": 63, "ph": "X", "name": "ReadAsync 3382", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776798, "dur": 2, "ph": "X", "name": "ProcessMessages 1283", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705776803, "dur": 238, "ph": "X", "name": "ReadAsync 1283", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705777045, "dur": 358, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705777406, "dur": 2, "ph": "X", "name": "ProcessMessages 2245", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705777408, "dur": 392, "ph": "X", "name": "ReadAsync 2245", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705777802, "dur": 636, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705778440, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705778537, "dur": 15, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705778554, "dur": 424, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705778979, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705778981, "dur": 339, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705779322, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705779324, "dur": 129, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705779455, "dur": 308, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705779772, "dur": 143, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705779918, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705780021, "dur": 183, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705780206, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705780632, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705780718, "dur": 74, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705780795, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705780883, "dur": 659, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705781544, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705781547, "dur": 48, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705781597, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705781683, "dur": 358, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782042, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782044, "dur": 159, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782205, "dur": 73, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782281, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782377, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782405, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782483, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705782487, "dur": 1392, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705783910, "dur": 14, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705784207, "dur": 731, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705803756, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705803761, "dur": 149, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705803913, "dur": 9, "ph": "X", "name": "ProcessMessages 3184", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705803923, "dur": 7145, "ph": "X", "name": "ReadAsync 3184", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705811073, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705811076, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705811271, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705812361, "dur": 4456, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705816820, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705816822, "dur": 7576, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705824402, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705824404, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705824509, "dur": 650, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705825202, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705825322, "dur": 402, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705825758, "dur": 269, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705826028, "dur": 333, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705826363, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705826625, "dur": 288, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705826920, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705826987, "dur": 1539, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705828567, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705828569, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705828847, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705828873, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705829134, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705829257, "dur": 536, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705829800, "dur": 1472, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705831283, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705831285, "dur": 1228, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705832515, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705832649, "dur": 790, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705833446, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705833450, "dur": 1066, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705834527, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705834531, "dur": 1012, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705835550, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705835554, "dur": 899, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705836459, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705836463, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705836585, "dur": 1569, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705838158, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705838159, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705838309, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705838508, "dur": 6221, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705844743, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705844751, "dur": 29728, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705874484, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705874487, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705874717, "dur": 480, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705875200, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705875376, "dur": 246, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705875625, "dur": 520, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705876148, "dur": 534, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705876683, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705876894, "dur": 558, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705877454, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705877694, "dur": 295, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705878000, "dur": 2537, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705880539, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705880700, "dur": 1970, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705882683, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705882910, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705883103, "dur": 630, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705883736, "dur": 391, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705884150, "dur": 406, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705884558, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705884638, "dur": 2857, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705887502, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705887504, "dur": 934, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705888441, "dur": 437, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705888880, "dur": 911, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705889795, "dur": 2720, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705892516, "dur": 517, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705893035, "dur": 130, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705893168, "dur": 189, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705893360, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705893516, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705893684, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705893900, "dur": 193, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705894096, "dur": 411, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705894517, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705894689, "dur": 261, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705894952, "dur": 709, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705895663, "dur": 614, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705896279, "dur": 2541, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705898824, "dur": 269, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899097, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899200, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899288, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899483, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899600, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899793, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899880, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705899955, "dur": 520, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705900479, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705900681, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705900764, "dur": 329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705901108, "dur": 166, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705901281, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705901286, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705901485, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705901487, "dur": 593, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705902083, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705902276, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705902371, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705902489, "dur": 481, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705902974, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705902977, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705903053, "dur": 140, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705903196, "dur": 112, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705903310, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705903349, "dur": 1042, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705904393, "dur": 362, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705904758, "dur": 136, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705904901, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705904905, "dur": 209, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705905127, "dur": 310, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705905439, "dur": 206, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705905648, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705905781, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705905817, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705905964, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705905967, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705906263, "dur": 498, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705906763, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705906943, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705906947, "dur": 199, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705907148, "dur": 497, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705907647, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705907834, "dur": 481, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705908319, "dur": 303, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705908648, "dur": 232, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705908884, "dur": 134, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705909020, "dur": 380, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705909402, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551705909403, "dur": 125011, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034421, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034424, "dur": 86, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034513, "dur": 51, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034604, "dur": 35, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034641, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034690, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034733, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706034756, "dur": 1894, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706036652, "dur": 2826, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706039480, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706039482, "dur": 379, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706039863, "dur": 376, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706040242, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706040271, "dur": 2341, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706042616, "dur": 315, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706042933, "dur": 924, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706043860, "dur": 1349, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706045212, "dur": 676, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706045891, "dur": 1644, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706047538, "dur": 1075, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706048615, "dur": 939, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706049556, "dur": 1151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706050709, "dur": 830, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706051546, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706051551, "dur": 1974, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706053530, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706053535, "dur": 1505, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706055043, "dur": 217, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706055264, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706055291, "dur": 689, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706055984, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706055987, "dur": 1767, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706057758, "dur": 1528, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706059308, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706059313, "dur": 276, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706059593, "dur": 1523, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706061120, "dur": 553, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706061678, "dur": 1003, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706062684, "dur": 1229, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706063920, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706063924, "dur": 679, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706064606, "dur": 2370, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706066982, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706066985, "dur": 334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067323, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067398, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067591, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067596, "dur": 175, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067774, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067880, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067931, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706067981, "dur": 19, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706068001, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706068102, "dur": 660, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706068772, "dur": 164, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706068938, "dur": 185, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706069129, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706069134, "dur": 169, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706069305, "dur": 150, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706069459, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706069461, "dur": 93, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706069557, "dur": 502, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070061, "dur": 198, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070265, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070270, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070315, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070378, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070450, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070628, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070630, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070720, "dur": 173, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070896, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070899, "dur": 96, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070997, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706070999, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071107, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071109, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071238, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071240, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071401, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071494, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071496, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071651, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071654, "dur": 155, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071811, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071813, "dur": 141, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071956, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706071959, "dur": 105, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072066, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072069, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072151, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072154, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072242, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072365, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072431, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072624, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072627, "dur": 186, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072816, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072818, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072903, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706072905, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073012, "dur": 202, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073247, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073312, "dur": 200, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073515, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073518, "dur": 125, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073658, "dur": 179, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073839, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073841, "dur": 133, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073977, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706073980, "dur": 271, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706074256, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706074503, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706074505, "dur": 571, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706075081, "dur": 575, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706075659, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706075662, "dur": 347100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706422784, "dur": 80, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706422866, "dur": 1155, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706424028, "dur": 1550, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706425581, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706425584, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706425705, "dur": 219455, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706645169, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706645172, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706645218, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706645240, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706645282, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706645308, "dur": 24, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706645334, "dur": 2891, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706648228, "dur": 51893, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706700128, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706700131, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706700178, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706700260, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706700311, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706700337, "dur": 29, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706700367, "dur": 3072, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706703441, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706703444, "dur": 279, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706703729, "dur": 23, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706703753, "dur": 240, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706703995, "dur": 378, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748551706704378, "dur": 3022, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 27554, "tid": 12988, "ts": 1748551706727081, "dur": 2299, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 27554, "tid": 8589934592, "ts": 1748551705520446, "dur": 283755, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748551705804205, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748551705804247, "dur": 8513, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 27554, "tid": 12988, "ts": 1748551706729385, "dur": 98, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 27554, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 27554, "tid": 4294967296, "ts": 1748551705350305, "dur": 1358805, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748551705365770, "dur": 135124, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748551706709247, "dur": 5935, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748551706712559, "dur": 74, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748551706715276, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 27554, "tid": 12988, "ts": 1748551706729486, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748551705662621, "dur": 10252, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748551705672880, "dur": 19573, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748551705692515, "dur": 72, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748551705692587, "dur": 117, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748551705692981, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705693407, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705693588, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705693714, "dur": 1028, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705695201, "dur": 2389, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705697638, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705697697, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705697931, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705698037, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705698106, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705698283, "dur": 1101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705699435, "dur": 131, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705699756, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705700269, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705703786, "dur": 43390, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748551705747735, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705747866, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705748436, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748551705749016, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_F0EDAB69F288A2F3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705751123, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748551705751630, "dur": 1131, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748551705753611, "dur": 340, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748551705754319, "dur": 224, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748551705769776, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748551705777355, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748551705692710, "dur": 84790, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748551705777508, "dur": 926354, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748551706703971, "dur": 50, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748551706704058, "dur": 957, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748551705692654, "dur": 84865, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705777525, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748551705777725, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705777806, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705778356, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705778475, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705778652, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705778753, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705778937, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705779187, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705779364, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705779517, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705779737, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705779933, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705780148, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705780319, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705780537, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705780698, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705780890, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705781166, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705781248, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705781372, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705781511, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705781654, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705781768, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705781931, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705782057, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705782281, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705782379, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705782434, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705782642, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705782742, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748551705783089, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705783363, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748551705783631, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705783738, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705783980, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705784131, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705784193, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705784729, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705785300, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705785384, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705785450, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705785538, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705785735, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705786022, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748551705786261, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705786319, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748551705786833, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705786904, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705787664, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705788392, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705789459, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705790295, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705791274, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705792347, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705793646, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705795502, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705796565, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705797672, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705799002, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705799977, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705801326, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705803046, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705804583, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705805636, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/SampleDependencyImportSystem/SampleList.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748551705805636, "dur": 2629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705808266, "dur": 2874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705811141, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705812841, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Events/MessageListenerEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748551705812178, "dur": 3285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705815463, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705816579, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705817732, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705818819, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705819625, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705820397, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705821176, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705821955, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705822754, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705823575, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705824428, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705825507, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705826665, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705826925, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705827042, "dur": 1849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705828891, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705828975, "dur": 4279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705833254, "dur": 1427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705834688, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705834743, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705834869, "dur": 3718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705838619, "dur": 37368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705875988, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705876069, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705878070, "dur": 4806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705882878, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705883171, "dur": 1399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705884614, "dur": 3530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705888145, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705888492, "dur": 5474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705893967, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705894136, "dur": 1566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705895733, "dur": 4807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705900541, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705900721, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705900772, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705900861, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705901547, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705903033, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705903445, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705903599, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705904472, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705904900, "dur": 2107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705907013, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748551705907194, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748551705907572, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551705907652, "dur": 128434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706036087, "dur": 3637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706039725, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706039815, "dur": 3847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706043663, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706043768, "dur": 4063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706047842, "dur": 2814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706050656, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706050718, "dur": 3312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706054031, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706054107, "dur": 3652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706057760, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706057939, "dur": 3210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706061151, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706061245, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706064706, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748551706064791, "dur": 10875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748551706075712, "dur": 628062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705692660, "dur": 84874, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705777544, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748551705777828, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705778353, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705778510, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705778697, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705778830, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705778925, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705779006, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705779192, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705779362, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705779437, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705779669, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705779777, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705779936, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705780071, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705780275, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705780456, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705780653, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705780823, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705781116, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705781209, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705781355, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705781519, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705781697, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705781816, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705781984, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705782066, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705782162, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705782273, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705782441, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705782558, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705782773, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705783118, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705783252, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705783559, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705783620, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705783729, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705784519, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705785102, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705785425, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705785491, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705786158, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748551705786728, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705786821, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705786887, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705787668, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705788382, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705789435, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705790287, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705791265, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705792338, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705793642, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705795539, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705796596, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705797786, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705799045, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705800066, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705801464, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705803139, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705804712, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705805680, "dur": 2553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705808233, "dur": 2919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705811152, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705812159, "dur": 3292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705815452, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705816564, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705817726, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705818843, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705819617, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705820379, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705821178, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705821942, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705822742, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705823543, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705824419, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705825458, "dur": 4540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748551705829998, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705830515, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705830888, "dur": 8038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748551705838927, "dur": 6842, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705861590, "dur": 12993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_CB2B1FA2A63FF8D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705874584, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705874716, "dur": 1939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748551705876656, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705876747, "dur": 6248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748551705882995, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705883213, "dur": 7319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1748551705890598, "dur": 1727, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706034404, "dur": 464, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551705892529, "dur": 142351, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1748551706036027, "dur": 2942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706038970, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706039054, "dur": 2797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706041893, "dur": 3318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706045212, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706045329, "dur": 4305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706049635, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706049754, "dur": 5306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706055061, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706055159, "dur": 3768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706058928, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706059025, "dur": 5483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706064509, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706064574, "dur": 9893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748551706074549, "dur": 628721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748551706703275, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748551706703352, "dur": 366, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748551705692665, "dur": 84899, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705777570, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705778338, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705778446, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705778840, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705778903, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705779095, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705779255, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705779373, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705779624, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705779694, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705779825, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705779952, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705780189, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705780316, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705780594, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705780692, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705780997, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705781143, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705781272, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705781382, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705781465, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705781664, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705781911, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705782043, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748551705782270, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705782369, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705782825, "dur": 2125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705784951, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705785354, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705785446, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705785525, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705786196, "dur": 23962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705810159, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705810864, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705811156, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705811338, "dur": 5323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705816709, "dur": 7318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705824027, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705824260, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705824319, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705824415, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705825301, "dur": 4648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705829949, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705830208, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705830338, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705830482, "dur": 2086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705832568, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705832747, "dur": 59451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705892199, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705892722, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705892914, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705892999, "dur": 5651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705898650, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705898876, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705898984, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705899106, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705899210, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705899275, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705899367, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705901444, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705901505, "dur": 1480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705902986, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705903353, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705903459, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705903956, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705904701, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705904813, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705905144, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705905816, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705905910, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551705907013, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748551705907172, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748551705907863, "dur": 128225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551706036089, "dur": 3307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706039446, "dur": 3271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706042718, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551706042777, "dur": 3181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706045959, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551706046051, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706048879, "dur": 4494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706053410, "dur": 3112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706056574, "dur": 4071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706060645, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551706060720, "dur": 6255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706066976, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748551706067188, "dur": 7919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748551706075157, "dur": 628626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705692674, "dur": 84907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705777585, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705778300, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705778514, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705778647, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705778805, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705778912, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705779014, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705779148, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705779362, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705779561, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705779675, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705779787, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705779892, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705780111, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705780224, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705780485, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705780622, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705780870, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705781076, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705781241, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705781333, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705781583, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705781775, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705781940, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705782054, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705782122, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705782200, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705782337, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705782407, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748551705782614, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705782844, "dur": 1996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705784840, "dur": 729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705785621, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705785821, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748551705786065, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705786250, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705786351, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748551705786734, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705786793, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705786856, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705787630, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705788338, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705789419, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705790266, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705791211, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705792293, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705793405, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705795462, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705796517, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705797573, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705798926, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705799911, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705801272, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705802909, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705804388, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705805581, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705806011, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705807746, "dur": 2771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705810517, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705812098, "dur": 3170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705815270, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705816484, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705817610, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705818703, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705819538, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705820301, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705821068, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705821846, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705822607, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705823412, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705824200, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705825044, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705825161, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705825258, "dur": 3868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705829127, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705829253, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705829353, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705830361, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705830570, "dur": 7319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705837889, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705838176, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705838256, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705838340, "dur": 36948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705875289, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705875364, "dur": 1576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705876979, "dur": 6666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705883645, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705883808, "dur": 4991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705888799, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705888937, "dur": 4435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705893372, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705893728, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705893854, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705893979, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705894767, "dur": 5077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705899845, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705900080, "dur": 1049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705901130, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705901257, "dur": 2198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705903455, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705903899, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705904531, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705905611, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748551705905957, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748551705906341, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551705907028, "dur": 129006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706036036, "dur": 3520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748551706039558, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706039675, "dur": 4089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748551706043765, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706043841, "dur": 3650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748551706047545, "dur": 3191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748551706050736, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706050839, "dur": 4507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748551706055392, "dur": 4450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748551706059843, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706059911, "dur": 13414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748551706073326, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706073490, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748551706073547, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706073842, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748551706073942, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748551706074028, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706074094, "dur": 1602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748551706075696, "dur": 628119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705692681, "dur": 84934, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705777620, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705778347, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705778498, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705778660, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705778758, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705778896, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705778979, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705779181, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705779308, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705779366, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705779511, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705779675, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705779746, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705779880, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705780035, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705780180, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705780419, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705780603, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705780800, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705780994, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705781173, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705781262, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705781398, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705781572, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705781737, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705781845, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705782046, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705782116, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705782224, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705782391, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705782471, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705782568, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705782639, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705782851, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705783025, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748551705783406, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705783605, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705783675, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705783838, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748551705784002, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705784110, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705784161, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705784662, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705785228, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705785307, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748551705785422, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705785487, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705785623, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705785774, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705786316, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705786382, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748551705786710, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705786820, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705786897, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705787670, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705788395, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705789463, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705790300, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705791143, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705792243, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705793227, "dur": 2207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705795435, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705796461, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705797546, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705798900, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705799885, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705801231, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705802830, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705804358, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705805541, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705805999, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705807312, "dur": 3030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705810356, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705812085, "dur": 3052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705815397, "dur": 1002, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1748551705816399, "dur": 1874, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1748551705818273, "dur": 650, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1748551705815138, "dur": 3786, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705818924, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705819720, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705820492, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705821252, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705822035, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705822843, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705823680, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705824583, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705825129, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705826456, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705826715, "dur": 1852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705828619, "dur": 3518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705832137, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705832359, "dur": 47521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705879880, "dur": 739, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705880625, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_1305DD42619CF31E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705880783, "dur": 5953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705886737, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705887191, "dur": 5298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705892489, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705892775, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705892910, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705893030, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705893126, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705893245, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705893305, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705893444, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705893559, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_CD3F41468EC5D7E9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705893658, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705893773, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705893873, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705893943, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705894594, "dur": 4410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705899005, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705899531, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705899681, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705899776, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705899911, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705899969, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705901035, "dur": 1157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705902192, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705902378, "dur": 3109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705905487, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705905602, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705905854, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705906799, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705907006, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705907225, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705907717, "dur": 700, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705908429, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705908516, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705908747, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551705908894, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748551705909060, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551705910014, "dur": 512456, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551706423351, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748551706423856, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748551706425545, "dur": 212, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551706645139, "dur": 258, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748551706426202, "dur": 219210, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748551706648293, "dur": 55469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705692688, "dur": 84951, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705777648, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705778163, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705778423, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705778531, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705778870, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705778930, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705779121, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705779263, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705779350, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705779415, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705779665, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705779769, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705779903, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705780131, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705780334, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705780545, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705780716, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705780962, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705781130, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705781233, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705781387, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705781501, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705781691, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705781891, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705782003, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748551705782228, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705782379, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705782448, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705782571, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705782682, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748551705783017, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748551705783335, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748551705783640, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705783707, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748551705784121, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705784225, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748551705785141, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748551705785294, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705785359, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705785436, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705785513, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705785596, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705785750, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748551705786091, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705786224, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705786415, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748551705786727, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705786931, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705787699, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705788409, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705789487, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705790308, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705791291, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705792339, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705793621, "dur": 1911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705795532, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705796588, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705797756, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705799041, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705800054, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705801421, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705803125, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705804685, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705805685, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705808248, "dur": 2889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705811137, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748551705811253, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705811419, "dur": 1202, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Plugin/Changelogs/Changelog_1_4_0.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748551705811419, "dur": 4286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705815705, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705816648, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705817853, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705818915, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705819711, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705820485, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705821260, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705822040, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705822878, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705823685, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705824587, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705825367, "dur": 4195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748551705829562, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705829741, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705829802, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705830545, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705830996, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705831086, "dur": 5016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748551705836102, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705836590, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705838946, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705839014, "dur": 36580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705875595, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705875669, "dur": 2046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705877715, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705877784, "dur": 5120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748551705882904, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705883153, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705884137, "dur": 17736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748551705901874, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705902100, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705902237, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705902645, "dur": 1816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748551705904461, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705904800, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_FE8D1F6CE20A2409.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705904956, "dur": 2062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705907019, "dur": 1889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551705908908, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748551705909102, "dur": 127419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706036521, "dur": 3445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706039967, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706040045, "dur": 3904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706043949, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706044007, "dur": 3486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706047535, "dur": 4034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706051570, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706051634, "dur": 3641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706055276, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706055344, "dur": 3985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706059331, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706059419, "dur": 3319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706062739, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706062884, "dur": 3892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706066777, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706067220, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706067299, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706067489, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706067796, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706068145, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706068255, "dur": 495, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnityResonance.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748551706068851, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069031, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069092, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069262, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069328, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069415, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069505, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069581, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069647, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706069708, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070040, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070140, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070241, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070312, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070378, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070443, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070577, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706070861, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071014, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071074, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071332, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071411, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071582, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071698, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071779, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706071935, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706072010, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748551706072104, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706072293, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706072357, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748551706072414, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706072526, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706072662, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706072811, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748551706072865, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706073071, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706073245, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706073389, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748551706073462, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706073635, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706073954, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748551706074073, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706074182, "dur": 349175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706423382, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748551706423359, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706425349, "dur": 199, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706700117, "dur": 315, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748551706426149, "dur": 274293, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748551706703262, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748551706703259, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748551706703350, "dur": 363, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748551706703716, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705692698, "dur": 84958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705777663, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705778140, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705778421, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705778569, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705778732, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705778853, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705778938, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705779019, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705779210, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705779399, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705779652, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705779715, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705779874, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705780052, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705780229, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705780405, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705780615, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705780740, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705781035, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705781171, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705781259, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705781364, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705781507, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705781656, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705781812, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705782002, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705782079, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705782136, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705782234, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705782389, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705782497, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748551705782730, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748551705782976, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748551705783263, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748551705783635, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705783754, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748551705783924, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748551705784027, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705784151, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705784300, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748551705785253, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748551705785516, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705785642, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705785726, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705785810, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748551705786234, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705786291, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748551705786428, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748551705786726, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705786929, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705787695, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705788403, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705789494, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705790312, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705791304, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705792361, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705793685, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705795537, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705796583, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705797713, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705799007, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705800002, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705801371, "dur": 1663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705803035, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705804553, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705805627, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.core@38fdc9b96878/Editor/SpeedTree8MaterialUpgrader.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748551705805627, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705808214, "dur": 2845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705811060, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748551705811145, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705811625, "dur": 1016, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Plugin/Migrations/Migration_1_6_to_1_7.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748551705811309, "dur": 4265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705815574, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705816597, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705817742, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705818823, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705819622, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705820368, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705821166, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705821939, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705822734, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705823547, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705824424, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705825612, "dur": 56693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748551705882305, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705882746, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_62EC740DD3AB3A8D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705882875, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705884177, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705884249, "dur": 8599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748551705892849, "dur": 773, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705893632, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705893722, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705893862, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705893930, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705894973, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705895029, "dur": 4123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748551705899152, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705899547, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705899672, "dur": 1446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705901160, "dur": 3872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748551705905033, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705905523, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705905659, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748551705906846, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705907054, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705907660, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705907725, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748551705908246, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705908372, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748551705908538, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748551705908836, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551705909006, "dur": 127089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706036096, "dur": 2734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706038831, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706038909, "dur": 3318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706042227, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706042372, "dur": 3054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706045479, "dur": 3215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706048695, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706048784, "dur": 3831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706052652, "dur": 3329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706055982, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706056040, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706058354, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706058445, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706061687, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706061822, "dur": 2044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706063867, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706064026, "dur": 7844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748551706071871, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706071960, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706072082, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706072295, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706072377, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706072499, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748551706072558, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706072672, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748551706072730, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706072941, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748551706073121, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706073325, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706073466, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706073659, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748551706073846, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706074022, "dur": 1112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748551706075135, "dur": 628795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705692705, "dur": 84968, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705777677, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705778322, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705778415, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705778494, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705778654, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705778777, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705778923, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705778999, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705779186, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705779313, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705779368, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705779533, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705779700, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705779796, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705780015, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705780157, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705780399, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705780561, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705780716, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705780909, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705781136, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705781257, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705781396, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705781557, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705781772, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705781922, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705782104, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705782199, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705782304, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705782423, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705782518, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705782580, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748551705782806, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748551705783124, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748551705783308, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748551705783591, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705783662, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705783987, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705784126, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748551705784539, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748551705785092, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748551705785269, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705785405, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705785467, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705785627, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705785720, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705785959, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705786307, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705786807, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705786875, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705787887, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705788614, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705789760, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705790593, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705791594, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705792665, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705794579, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705795856, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705796869, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705798326, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705799364, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705800418, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705801767, "dur": 1665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705803432, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705805053, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705805780, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705806236, "dur": 2729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705808965, "dur": 2737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705811703, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Options/UnitOptionUtility.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748551705812712, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/VisualScripting.Flow/Options/UnitOptionFilter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748551705811703, "dur": 3778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705815482, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705816587, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705817734, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705818808, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705819613, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705820360, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705821150, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705821925, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705822731, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705823539, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705824401, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705825154, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705825864, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705828593, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705828883, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_B533E0F362656246.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705828959, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705829081, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705830389, "dur": 2769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705833158, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705833413, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_F0EDAB69F288A2F3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705833492, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705833864, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705836211, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705836354, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705838606, "dur": 36632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705875277, "dur": 2214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705877491, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705877549, "dur": 6618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705884168, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705884744, "dur": 4942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705889687, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705889829, "dur": 4763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705894593, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705894761, "dur": 1546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705896328, "dur": 3867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705900196, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705900536, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705900593, "dur": 1815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705902409, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705902531, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705902635, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748551705903102, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705903159, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748551705903210, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705903299, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705903431, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705903915, "dur": 561, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Core/EditorBinding/NullMeansSelfAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748551705903577, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705904513, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705905540, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748551705905869, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748551705906568, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551705907041, "dur": 128986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706036033, "dur": 4278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748551706040312, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706040372, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748551706042995, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706043057, "dur": 4518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748551706047576, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706047649, "dur": 4170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748551706051820, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706051912, "dur": 3697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748551706055610, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706055664, "dur": 6883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748551706062548, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706062633, "dur": 9976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748551706072610, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706072803, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748551706072858, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706072963, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748551706073015, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706073167, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748551706073222, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706073338, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748551706073401, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706073522, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706073657, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748551706073841, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706074015, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706074536, "dur": 573723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748551706648299, "dur": 55609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748551706706669, "dur": 557, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 27554, "tid": 12988, "ts": 1748551706730978, "dur": 19433, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 27554, "tid": 12988, "ts": 1748551706750562, "dur": 1999, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 27554, "tid": 12988, "ts": 1748551706721594, "dur": 35550, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}