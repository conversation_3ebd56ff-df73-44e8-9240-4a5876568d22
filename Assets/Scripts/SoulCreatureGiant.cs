using UnityEngine;
using System.Collections.Generic;
using FMODUnity;
using Random = UnityEngine.Random;
using System;

/// <summary>
/// SoulCreatureGiant - A large soul creature entity that interacts with the player
/// This is a refactored version with modular components for better organization
/// </summary>
[RequireComponent(typeof(ParticleSystem))]
public class SoulCreatureGiant : MonoBehaviour, ISoulCreatureBoostProvider
{
    #region Static Registry
    // Static registry of all active SoulCreatureGiant instances
    private static readonly List<SoulCreatureGiant> ActiveInstances = new();

    // Static method to get all active instances
    public static List<SoulCreatureGiant> GetAllActiveInstances()
    {
        return ActiveInstances;
    }
    #endregion

    #region Core Components and References
    // Core references
    private Transform playerTransform;
    private PlayerController playerController;
    private ParticleSystem ps;
    private Camera mainCamera;

    // Flag to track initialization status
    [HideInInspector] public bool isFullyInitialized = false;

    // Boundary values for movement constraints
    [SerializeField] public float minY = 25;
    [SerializeField] public float maxY = 50;

    // Public access to average particle position
    [HideInInspector] public Vector3 averageParticlePosition;

    [Header("Particle System Settings")]
    [Tooltip("Minimum number of max particles")]
    [SerializeField] private int minMaxParticles = 200;

    [Tooltip("Maximum number of max particles")]
    [SerializeField] private int maxMaxParticles = 1000;

    [SerializeField] private float soulCreatureSizeMin = 10.0f;
    [SerializeField] private float soulCreatureSizeMax = 15.0f;

    // Color settings for ocean creatures
    [Header("Ocean Creature Color Settings")]
    [Tooltip("Whether this is an ocean-type creature that should have random colors")]
    public bool isOceanCreature = false;

    [Tooltip("Minimum color hue (0-1)")]
    [Range(0f, 1f)] public float minHue = 0.5f;

    [Tooltip("Maximum color hue (0-1)")]
    [Range(0f, 1f)] public float maxHue = 0.7f;

    [Tooltip("Saturation for the color (0-1)")]
    [Range(0f, 1f)] public float colorSaturation = 0.8f;

    [Tooltip("Brightness for the color (0-1)")]
    [Range(0f, 1f)] public float colorBrightness = 0.8f;

    // Render queue settings for ocean creatures
    [Header("Render Queue Settings")]
    [Tooltip("Whether to use the centralized render queue system in GameManager")]
    public bool useCentralizedRenderQueue = true;
    #endregion

    #region Module Toggles
    [Header("Module Toggles")]
    [Tooltip("Enable/disable particle following behavior")]
    public bool enableParticleFollowing = true;
    #endregion

    #region Component References
    // Component references
    [Header("Component References")]
    [SerializeField] public ParticleFollowingComponent particleFollowing = new();

    [Header("Flight Boost")]
    [Tooltip("Amount of flight boost this soul creature provides to the player")]
    [SerializeField] public float flightBoostValue = 6f;

    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;
    #endregion

    #region Gravity Control Settings
    [Header("Gravity Control Settings")]
    [Tooltip("Number of particle collisions required to disable player gravity")]
    [SerializeField] private int particleCollisionsToDisableGravity = 10;

    [Tooltip("Time in seconds after which gravity is re-enabled if no particle collisions occur")]
    [SerializeField] private float gravityReenableDelay = 1.0f;

    [Tooltip("Enable debug logging for gravity control")]
    [SerializeField] private bool debugGravityControl = false;

    // Gravity control variables
    private int currentParticleCollisionCount = 0;
    private float lastParticleCollisionTime = -Mathf.Infinity;
    private bool isGravityDisabled = false;
    #endregion

    #region Unity Lifecycle Methods
    private void Awake()
    {
        if (enableParticleFollowing)
        {
            InitializeParticleSystem();
        }
    }

    private void OnEnable()
    {
        // Register this instance in the static list
        if (!ActiveInstances.Contains(this))
        {
            ActiveInstances.Add(this);
        }
    }

    private void OnDisable()
    {
        // Remove this instance from the static list
        ActiveInstances.Remove(this);

        // Clean up gravity control
        CleanupGravityControl();
    }

    private void Start()
    {
        // Get references from GameManager
        if (GameManager.Instance != null)
        {
            var gm = GameManager.Instance;
            playerTransform = gm.player.transform;
            playerController = gm.player;
            minY = gm.oceanBottom.transform.position.y;
            maxY = gm.cloudsBottom.transform.position.y;
        }
        else
        {
            Debug.LogWarning("GameManager instance not found!");
            enabled = false;
            return;
        }

        // Get main camera reference
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogWarning("Main camera not found!");
        }

        // Make sure the player from GameManager has the Player tag
        if (playerTransform != null && !playerTransform.CompareTag("Player"))
        {
            Debug.LogWarning($"Player GameObject from GameManager doesn't have the 'Player' tag! Current tag: {playerTransform.tag}");
            playerTransform.tag = "Player";
            Debug.Log("Set player tag to 'Player'");
        }

        // Initialize particle system
        ps = GetComponent<ParticleSystem>();
        if (ps == null && enableParticleFollowing)
        {
            Debug.LogError("No ParticleSystem found on this GameObject!");
            enabled = false;
            return;
        }

        // Randomize particle system max particles
        var main = ps.main;
        int randomMaxParticles = Random.Range(minMaxParticles, maxMaxParticles + 1);
        main.maxParticles = randomMaxParticles;

        // Initialize components
        if (enableParticleFollowing)
        {
            particleFollowing.Initialize(transform, ps);
        }

        // Apply random color for ocean creatures
        if (isOceanCreature && ps != null)
        {
            ApplyRandomColor();
        }

        // Mark as fully initialized
        isFullyInitialized = true;
//        Debug.Log($"SoulCreatureGiant {gameObject.name} fully initialized. isOceanCreature: {isOceanCreature}");
    }

    private void Update()
    {
        if (playerTransform == null || playerController == null)
        {
                // Try to get references if they're missing
            if (GameManager.Instance != null)
            {
                playerTransform = GameManager.Instance.player.transform;
                playerController = GameManager.Instance.player;

                if (playerTransform == null || playerController == null)
                {
                    Debug.LogWarning($"SoulCreatureGiant {gameObject.name}: Could not find player references from GameManager");
                    return;
                }
            }
            else
            {
                Debug.LogWarning($"SoulCreatureGiant {gameObject.name}: GameManager.Instance is null");
                return;
            }
        }

        // Check if particle system is valid
        if (ps == null)
        {
            ps = GetComponent<ParticleSystem>();
            if (ps == null)
            {
                Debug.LogWarning($"SoulCreatureGiant {gameObject.name}: ParticleSystem is null");
                return;
            }
        }

        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);

        // Update particle following
        if (enableParticleFollowing && ps != null && particleFollowing != null)
        {
            try
            {
                averageParticlePosition = particleFollowing.GetAverageParticlePosition();
                particleFollowing.UpdateParticles(clampedDeltaTime);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"SoulCreatureGiant {gameObject.name}: Error in particle following: {e.Message}");
            }
        }



        // Update render queue for ocean creatures
        if (isOceanCreature && useCentralizedRenderQueue && ps != null && GameManager.Instance != null)
        {
            try
            {
                UpdateRenderQueue();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"SoulCreatureGiant {gameObject.name}: Error in render queue: {e.Message}");
            }
        }

        // Check if we need to re-enable gravity
        try
        {
            CheckGravityReenabling();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"SoulCreatureGiant {gameObject.name}: Error in gravity reenabling: {e.Message}");
        }
    }

    private void OnParticleCollision(GameObject other)
    {
        if (other.CompareTag("Player"))
        {
            // Handle gravity control
            HandleGravityControl();
        }
    }

    private void OnDestroy()
    {
        // Clean up any active audio events
        StopAllCoroutines();

        // Clean up materials from the centralized render queue system
        if (isOceanCreature && useCentralizedRenderQueue && GameManager.Instance != null)
        {
            ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();
            foreach (ParticleSystemRenderer renderer in renderers)
            {
                foreach (Material material in renderer.materials)
                {
                    if (material != null)
                    {
                        GameManager.Instance.RemoveDynamicRenderQueueMaterial(material);
                    }
                }
            }
        }

        // Clean up gravity control
        CleanupGravityControl();
    }
    #endregion

    public float GetFlightBoostValue() => flightBoostValue;



    #region Initialization Methods
    private void InitializeParticleSystem()
    {
        ps = GetComponent<ParticleSystem>();
        if (ps == null)
        {
            Debug.LogError($"No ParticleSystem found on {gameObject.name}!");
            return;
        }

        // Make the emitting mesh bigger
        var shapeModule = ps.shape;
        if (shapeModule.shapeType != ParticleSystemShapeType.Mesh)
        {
            Debug.LogWarning($"ParticleSystem on {gameObject.name} does not use a mesh shape type. Current shape type: {shapeModule.shapeType}");
            return;
        }

        float randomScale = Random.Range(soulCreatureSizeMin, soulCreatureSizeMax);
        shapeModule.scale = new Vector3(randomScale, randomScale, randomScale);

//        Debug.Log($"Initialized particle system for {gameObject.name} with scale {randomScale}");
    }
    #endregion

    #region Color Randomization
    private void ApplyRandomColor()
    {
        // Generate a random hue in the specified range
        float randomHue = Random.Range(minHue, maxHue);

        // Create a color using HSV (Hue, Saturation, Value)
        Color randomColor = Color.HSVToRGB(randomHue, colorSaturation, colorBrightness);

        // Apply the color to the particle system
        var main = ps.main;
        main.startColor = randomColor;

        // Also apply to any child particle systems
        ParticleSystem[] childParticleSystems = GetComponentsInChildren<ParticleSystem>();
        foreach (ParticleSystem childPs in childParticleSystems)
        {
            if (childPs != ps) // Skip the main particle system we already modified
            {
                var childMain = childPs.main;
                childMain.startColor = randomColor;
            }
        }
    }
    #endregion

    #region Render Queue Management
    private void UpdateRenderQueue()
    {
        // Get all renderers (main and child particle systems)
        ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();

        // Register all materials with the centralized system
        foreach (ParticleSystemRenderer renderer in renderers)
        {
            foreach (Material material in renderer.materials)
            {
                if (material != null)
                {
                    GameManager.Instance.AddDynamicRenderQueueMaterial(material);
                }
            }
        }
    }
    #endregion

    #region Gravity Control
    private void HandleGravityControl()
    {
        // Update collision count and time
        currentParticleCollisionCount++;
        lastParticleCollisionTime = Time.time;

        // Check if we've reached the threshold to disable gravity
        if (!isGravityDisabled && currentParticleCollisionCount >= particleCollisionsToDisableGravity)
        {
            if (playerController != null)
            {
                playerController.SetGravityDisabled(true);
                isGravityDisabled = true;

                if (debugGravityControl)
                {
                    Debug.Log($"Disabled gravity for player due to {gameObject.name} particle interactions");
                }
            }
        }
    }

    private void CheckGravityReenabling()
    {
        // Only check if gravity is currently disabled by this soul creature
        if (!isGravityDisabled) return;

        // Check if enough time has passed since the last particle collision
        if (Time.time - lastParticleCollisionTime >= gravityReenableDelay)
        {
            // Reset collision count
            currentParticleCollisionCount = 0;

            // Re-enable gravity if the player controller exists
            if (playerController != null)
            {
                // We need to check if any other SoulCreatureGiant is currently disabling gravity
                bool otherCreatureDisablingGravity = false;

                // Check all other active SoulCreatureGiant objects using our static list
                foreach (SoulCreatureGiant creature in ActiveInstances)
                {
                    // Skip this creature
                    if (creature == this) continue;

                    // Check if this other creature is disabling gravity
                    if (creature.isGravityDisabled)
                    {
                        otherCreatureDisablingGravity = true;
                        break;
                    }
                }

                // Only re-enable gravity if no other creature is disabling it
                if (!otherCreatureDisablingGravity)
                {
                    playerController.SetGravityDisabled(false);

                    if (debugGravityControl)
                    {
                        Debug.Log($"Re-enabled gravity for player after no interactions with {gameObject.name} for {gravityReenableDelay} seconds");
                    }
                }
                else if (debugGravityControl)
                {
                    Debug.Log($"{gameObject.name} would re-enable gravity, but another soul creature is still disabling it");
                }

                // Mark this creature as no longer disabling gravity
                isGravityDisabled = false;
            }
        }
    }

    private void CleanupGravityControl()
    {
        // Only do cleanup if this soul creature is currently disabling gravity
        if (isGravityDisabled && playerController != null)
        {
            // We need to check if any other SoulCreatureGiant is currently disabling gravity
            bool otherCreatureDisablingGravity = false;

            // Check all other active SoulCreatureGiant objects using our static list
            foreach (SoulCreatureGiant creature in ActiveInstances)
            {
                // Skip this creature
                if (creature == this) continue;

                // Check if this other creature is disabling gravity
                if (creature.isGravityDisabled)
                {
                    otherCreatureDisablingGravity = true;
                    break;
                }
            }

            // Only re-enable gravity if no other creature is disabling it
            if (!otherCreatureDisablingGravity)
            {
                playerController.SetGravityDisabled(false);

                if (debugGravityControl)
                {
                    Debug.Log($"Re-enabled gravity for player due to {gameObject.name} being disabled or destroyed");
                }
            }

            // Reset state
            isGravityDisabled = false;
            currentParticleCollisionCount = 0;
        }
    }
    #endregion

    #region Component Classes
    /// <summary>
    /// Component that handles particle following behavior for SoulCreatureGiant
    /// </summary>
    [System.Serializable]
    public class ParticleFollowingComponent
    {
        #region Settings
        [Header("Particle Following Settings")]
        [Tooltip("Minimum speed particles can move at")]
        [SerializeField] public float minSpeed = 1f;

        [Tooltip("Maximum speed particles can move at")]
        [SerializeField] public float maxSpeed = 10f;

        [Tooltip("Rate at which particle speed interpolates to desired speed")]
        [SerializeField] public float lerpFactor = 2f;

        [Tooltip("Distance from target where particles start following")]
        [SerializeField] public float followThreshold = 1f;

        [Tooltip("Distance from target where particles stop following")]
        [SerializeField] public float stopThreshold = 0.3f;

        [Tooltip("Maximum distance at which speed scaling reaches its maximum effect")]
        [SerializeField] public float maxDistance = 5f;

        [Header("Particle Following Gizmos")]
        [Tooltip("Toggle to show or hide debug visuals in the Scene view")]
        [SerializeField] public bool showGizmos = true;

        [Tooltip("Size of the target position gizmo sphere")]
        [SerializeField, Range(0.05f, 1.0f)] public float gizmoScale = 0.3f;

        [Header("Particle Update Rate")]
        [Tooltip("How often (in seconds) to update particle movement. Lower is smoother, higher is more performant.")]
        [SerializeField] public float particleUpdateInterval = 0.01f;
        private float particleUpdateTimer = 0f;
        #endregion

        #region Internal Data
        // Internal data structure for particle tracking
        public struct ParticleData
        {
            public Vector3 TargetLocalPos;
            public float DesiredSpeed;
            public float CurrentSpeed;
            public float Timer;
            public bool IsFollowing;
            public bool initialized; // Robust initialization flag
            public Vector3 Velocity; // Velocity for smooth movement
        }

        // Internal variables
        public Mesh shapeMesh;
        public ParticleSystem.Particle[] particlesArray;
        public ParticleData[] particlesData; // Use array instead of dictionary
        public float[] triangleAreas;
        public float totalArea;
        #endregion

        // Reference to the parent transform and particle system
        private Transform parentTransform;
        private ParticleSystem particleSystem;

        // Initialize the component
        public void Initialize(Transform transform, ParticleSystem ps)
        {
            parentTransform = transform;
            particleSystem = ps;

            // Initialize particle array
            int maxParticles = ps.main.maxParticles;
            particlesArray = new ParticleSystem.Particle[maxParticles];
            particlesData = new ParticleData[maxParticles];

            // Get the mesh from the particle system
            if (ps.shape.shapeType != ParticleSystemShapeType.Mesh || (shapeMesh = ps.shape.mesh) == null)
            {
                Debug.LogWarning($"Particle System on {transform.name} must use a Mesh shape with a valid mesh assigned.");
                return;
            }

            // Precompute triangle areas for efficient random point generation
            PrecomputeTriangleAreas();
        }

        // Precompute triangle areas for efficient random point generation
        public void PrecomputeTriangleAreas()
        {
            if (shapeMesh == null) return;

            int[] tris = shapeMesh.triangles;
            Vector3[] verts = shapeMesh.vertices;
            int triangleCount = tris.Length / 3;
            triangleAreas = new float[triangleCount];
            totalArea = 0f;

            for (int i = 0; i < triangleCount; i++)
            {
                Vector3 v0 = verts[tris[i * 3]];
                Vector3 v1 = verts[tris[i * 3 + 1]];
                Vector3 v2 = verts[tris[i * 3 + 2]];

                // Calculate the area of the triangle
                float area = 0.5f * Vector3.Cross(v1 - v0, v2 - v0).magnitude;
                triangleAreas[i] = area;
                totalArea += area;
            }
        }

        // Get a random point on the mesh based on triangle area weighting
        public Vector3 GetRandomMeshPoint()
        {
            if (shapeMesh == null) return Vector3.zero;

            int[] tris = shapeMesh.triangles;
            Vector3[] verts = shapeMesh.vertices;

            float randomValue = Random.value * totalArea;
            int triIndex = 0;
            float cumulativeArea = 0f;

            for (int i = 0; i < triangleAreas.Length; i++)
            {
                cumulativeArea += triangleAreas[i];
                if (randomValue <= cumulativeArea)
                {
                    triIndex = i * 3;
                    break;
                }
            }

            Vector3 v0 = verts[tris[triIndex]];
            Vector3 v1 = verts[tris[triIndex + 1]];
            Vector3 v2 = verts[tris[triIndex + 2]];

            float u = Random.value, v = Random.value;
            if (u + v > 1) { u = 1 - u; v = 1 - v; }

            // Get the base point on the mesh
            Vector3 basePoint = v0 + u * (v1 - v0) + v * (v2 - v0);

            // Apply scaling to the target point based on the particle system's shape scale
            if (particleSystem != null)
            {
                float scaleFactor = particleSystem.shape.scale.x;
                // Scale the point from the center of the mesh
                basePoint *= scaleFactor;
            }

            return basePoint;
        }

        // Initialize data for a new particle
        public ParticleData InitializeParticleData()
        {
            Vector3 localPos = GetRandomMeshPoint();
            float speed = Random.Range(minSpeed, maxSpeed);
            return new ParticleData
            {
                TargetLocalPos = localPos,
                DesiredSpeed = speed,
                CurrentSpeed = speed,
                Timer = Random.Range(0f, 4f),
                IsFollowing = false,
                Velocity = Vector3.zero // Initialize velocity
            };
        }

        // Update the speed of a particle
        public void UpdateParticleSpeed(ref ParticleData data, float deltaTime)
        {
            data.Timer += deltaTime;
            if (data.Timer > 4f)
            {
                data.Timer -= 4f;
                data.DesiredSpeed = Random.Range(minSpeed, maxSpeed);
            }
            data.CurrentSpeed = Mathf.Lerp(data.CurrentSpeed, data.DesiredSpeed, lerpFactor * deltaTime);
        }

        // Update all particles
        public void UpdateParticles(float deltaTime)
        {
            particleUpdateTimer += deltaTime;
            if (particleUpdateTimer < particleUpdateInterval)
                return;
            float step = particleUpdateTimer;
            particleUpdateTimer = 0f;

            if (particleSystem == null || shapeMesh == null) return;
            int count = particleSystem.GetParticles(particlesArray);
            Vector3 cachedTransformPos = parentTransform.position;
            Quaternion cachedTransformRot = parentTransform.rotation;
            Vector3 cachedTransformScale = parentTransform.lossyScale;

            for (int i = 0; i < count; i++)
            {
                ref ParticleSystem.Particle particle = ref particlesArray[i];
                ref ParticleData data = ref particlesData[i];

                if (!data.initialized)
                {
                    data = InitializeParticleData();
                    data.initialized = true;
                    data.Velocity = Vector3.zero;
                }

                UpdateParticleSpeed(ref data, step);

                Vector3 targetWorldPos = parentTransform.TransformPoint(data.TargetLocalPos);
                float distance = Vector3.Distance(particle.position, targetWorldPos);

                data.IsFollowing = data.IsFollowing ? distance >= stopThreshold : distance > followThreshold;

                if (data.IsFollowing)
                {
                    Vector3 toTarget = (targetWorldPos - particle.position);
                    float dist = toTarget.magnitude;
                    Vector3 desiredVelocity = Vector3.zero;
                    if (dist > 0.001f)
                    {
                        float speed = data.CurrentSpeed * Mathf.Min(dist / maxDistance, 1f);
                        if (dist >= maxDistance)
                        {
                            float speedScaleFactor = Mathf.Max(dist / maxDistance, 1);
                            speed = (data.CurrentSpeed + 10) * speedScaleFactor;
                        }
                        desiredVelocity = toTarget.normalized * speed;
                    }
                    data.Velocity = Vector3.Lerp(data.Velocity, desiredVelocity, lerpFactor * step);
                    if (dist < stopThreshold * 2f)
                    {
                        data.Velocity *= Mathf.Lerp(0.1f, 1f, dist / (stopThreshold * 2f));
                    }
                    particle.position += data.Velocity * step;
                }
                else
                {
                    data.Velocity = Vector3.Lerp(data.Velocity, Vector3.zero, lerpFactor * step);
                }
            }
            particleSystem.SetParticles(particlesArray, count);
        }

        // Calculate the average position of all particles
        public Vector3 GetAverageParticlePosition()
        {
            if (particleSystem == null) return Vector3.zero;

            int count = particleSystem.GetParticles(particlesArray);
            if (count == 0) return Vector3.zero;

            Vector3 sumPosition = Vector3.zero;
            for (int i = 0; i < count; i++)
            {
                sumPosition += particlesArray[i].position;
            }
            return sumPosition / count;
        }
    }





    #endregion
}
