using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Manages the dynamic spawning of creatures based on environment, distance from player,
/// and configurable spawn rules.
/// </summary>
public class CreatureSpawner : MonoBehaviour
{
    #region Prefab Configuration

    [System.Serializable]
    public class CreaturePrefab
    {
        [Tooltip("The creature prefab to spawn")]
        public GameObject prefab;

        [Tooltip("Name for easier identification in the inspector")]
        public string prefabName;

        [Range(0, 100)]
        [Tooltip("Chance of this prefab being selected when spawning (0-100%)")]
        public float spawnChance = 20f;

        [Tooltip("The environment this prefab belongs to")]
        public EnvironmentType environment;

        [Tooltip("Minimum Y position for optimal spawning")]
        public float minOptimalY;

        [Tooltip("Maximum Y position for optimal spawning")]
        public float maxOptimalY;

        [Header("Custom Spawn Settings")]

        [Tooltip("Whether this creature should use a custom spawn distance range")]
        public bool useCustomSpawnDistance = false;

        [Tooltip("Minimum custom spawn distance for this creature (only used if useCustomSpawnDistance is true)")]
        public float minCustomSpawnDistance = 30f;

        [Tooltip("Maximum custom spawn distance for this creature (only used if useCustomSpawnDistance is true)")]
        public float maxCustomSpawnDistance = 50f;

        [Tooltip("Whether this creature should use a custom despawn distance")]
        public bool useCustomDespawnDistance = false;

        [Tooltip("Custom despawn distance for this creature (only used if useCustomDespawnDistance is true)")]
        public float customDespawnDistance = 50f;

        [Tooltip("Whether to limit the maximum number of instances of this prefab")]
        public bool useMaxCountLimit = false;

        [Tooltip("Maximum number of instances of this prefab that can exist at once (only used if useMaxCountLimit is true)")]
        public int maxInstances = 5;

        [Tooltip("Whether to enable a cooldown period after spawning this prefab")]
        public bool useSpawnCooldown = false;

        [Tooltip("Duration in seconds before this prefab can be spawned again (only used if useSpawnCooldown is true)")]
        public float spawnCooldownDuration = 10f;

        [Header("Environment-Specific Spawning")]

        [Tooltip("Only spawn this creature if the player is in a specific environment")]
        public bool onlySpawnInPlayerEnvironment = false;

        [Tooltip("The environment the player must be in for this creature to spawn (only used if onlySpawnInPlayerEnvironment is true)")]
        public EnvironmentType requiredPlayerEnvironment;

        [Tooltip("Internal pool of instantiated objects")]
        [HideInInspector]
        public List<GameObject> pooledObjects = new List<GameObject>();
    }

    [Header("Prefab Settings")]
    [Tooltip("List of creature prefabs that can be spawned")]
    public List<CreaturePrefab> prefabs = new List<CreaturePrefab>();

    [Tooltip("Maximum number of creatures that can exist at once")]
    public int maxConcurrentNPCs = 30;

    #endregion

    #region Spawn Settings

    [Header("Spawn Settings")]
    [Tooltip("Distance from player to spawn creatures (should be just beyond fog visibility)")]
    public float spawnDistance = 30f;

    [Tooltip("Maximum distance from player before creatures are despawned")]
    public float despawnDistance = 50f;

    [Tooltip("Gradient width for Y-level spawn probability (in units)")]
    public float yGradientWidth = 5f;

    [Tooltip("How often to check for spawning new creatures (in seconds)")]
    public float spawnCheckInterval = 0.5f;

    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;

    #endregion

    #region Debug Settings

    #endregion

    #region Private Variables

    private Transform playerTransform;
    private EnvironmentManager environmentManager;
    private int activeNPCCount = 0;
    private List<GameObject> activeNPCs = new List<GameObject>();
    private Dictionary<GameObject, CreaturePrefab> npcToPrefabMap = new Dictionary<GameObject, CreaturePrefab>();

    // Dictionary to track cooldown timers for each prefab
    private Dictionary<CreaturePrefab, float> prefabCooldownTimers = new Dictionary<CreaturePrefab, float>();

    #endregion

    #region Unity Lifecycle

    private void Start()
    {
        // Get references
        if (GameManager.Instance != null)
        {
            playerTransform = GameManager.Instance.player.transform;
        }
        else
        {
            playerTransform = GameObject.FindGameObjectWithTag("Player")?.transform;
            if (playerTransform == null)
            {
                Debug.LogError("CreatureSpawner: Player not found!");
                enabled = false;
                return;
            }
        }

        environmentManager = EnvironmentManager.Instance;
        if (environmentManager == null)
        {
            Debug.LogError("CreatureSpawner: EnvironmentManager not found!");
            enabled = false;
            return;
        }

        // Initialize object pools
        InitializeObjectPools();

        // Start spawning coroutine
        StartCoroutine(SpawnCheckRoutine());
    }

    private void Update()
    {
        // Check for creatures that need to be despawned
        CheckForDespawns();

        // Update cooldown timers
        UpdateCooldownTimers();
    }

    // Reusable collections to avoid garbage allocation
    private List<CreaturePrefab> reusableExpiredCooldowns = new List<CreaturePrefab>();
    private List<CreaturePrefab> reusablePrefabKeys = new List<CreaturePrefab>();

    /// <summary>
    /// Updates all active cooldown timers, reducing them by Time.deltaTime
    /// and removing expired timers from the dictionary.
    /// </summary>
    private void UpdateCooldownTimers()
    {
        // Clear and reuse existing lists to avoid allocation
        reusableExpiredCooldowns.Clear();
        reusablePrefabKeys.Clear();

        // Copy keys to reusable list
        reusablePrefabKeys.AddRange(prefabCooldownTimers.Keys);

        // Update all cooldown timers
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        foreach (var prefab in reusablePrefabKeys)
        {
            float remainingTime = prefabCooldownTimers[prefab] - clampedDeltaTime;

            if (remainingTime <= 0)
            {
                // Cooldown has expired
                reusableExpiredCooldowns.Add(prefab);
            }
            else
            {
                // Update the remaining time
                prefabCooldownTimers[prefab] = remainingTime;
            }
        }

        // Remove expired cooldowns
        foreach (var prefab in reusableExpiredCooldowns)
        {
            prefabCooldownTimers.Remove(prefab);
        }
    }

    #endregion

    #region Object Pooling

    private void InitializeObjectPools()
    {
        // Pre-instantiate a few objects for each prefab type
        foreach (var prefabConfig in prefabs)
        {
            if (prefabConfig.prefab == null) continue;

            // Create initial pool (3 of each type)
            for (int i = 0; i < 3; i++)
            {
                GameObject obj = Instantiate(prefabConfig.prefab);
                obj.SetActive(false);
                prefabConfig.pooledObjects.Add(obj);
            }
        }
    }

    private GameObject GetPooledObject(CreaturePrefab prefabConfig)
    {
        // Check if there's an available object in the pool
        foreach (var obj in prefabConfig.pooledObjects)
        {
            if (!obj.activeInHierarchy)
            {
                return obj;
            }
        }

        // If no object is available, create a new one
        GameObject newObj = Instantiate(prefabConfig.prefab);
        newObj.SetActive(false);
        prefabConfig.pooledObjects.Add(newObj);
        return newObj;
    }

    private void ReturnToPool(GameObject obj)
    {
        if (obj == null) return;

        // Reset SoulCreatureLogic state to prevent stretched particles when reused
        SoulCreatureLogic soulCreature = obj.GetComponent<SoulCreatureLogic>();
        if (soulCreature != null)
        {
            soulCreature.ResetForPooling();
        }

        // Reset the object's state
        obj.SetActive(false);

        // Remove from active NPCs list
        if (activeNPCs.Contains(obj))
        {
            activeNPCs.Remove(obj);
            activeNPCCount--;
        }

        // Remove from mapping
        if (npcToPrefabMap.ContainsKey(obj))
        {
            npcToPrefabMap.Remove(obj);
        }
    }

    #endregion

    #region Spawn Logic

    private IEnumerator SpawnCheckRoutine()
    {
        while (true)
        {
            // Only spawn if we're below the maximum
            if (activeNPCCount < maxConcurrentNPCs)
            {
                SpawnNPC();
            }

            yield return new WaitForSeconds(spawnCheckInterval);
        }
    }

    /// <summary>
    /// Spawns a new creature based on spawn chance and environment rules.
    /// </summary>
    public void SpawnNPC()
    {
        // Select a prefab based on spawn chance
        CreaturePrefab selectedPrefab = SelectPrefabByChance();
        if (selectedPrefab == null)
        {
            return;
        }

        // Check if this prefab has a cooldown enabled and is currently in cooldown
        if (selectedPrefab.useSpawnCooldown && prefabCooldownTimers.ContainsKey(selectedPrefab))
        {
            // Prefab is in cooldown, skip spawning
            //Debug.Log($"Cannot spawn {selectedPrefab.prefabName}: Cooldown active (Remaining: {prefabCooldownTimers[selectedPrefab]:F1} seconds).");
            return;
        }

        // Check if this prefab has MAX COUNT limit
        if (selectedPrefab.useMaxCountLimit)
        {
            // Count current active instances of this prefab
            int currentInstanceCount = CountActiveInstancesOfPrefab(selectedPrefab);

            // Skip spawning if we've reached the max instances for this prefab
            if (currentInstanceCount >= selectedPrefab.maxInstances)
            {
                //Debug.Log($"Cannot spawn {selectedPrefab.prefabName}: Maximum instances limit reached ({currentInstanceCount}/{selectedPrefab.maxInstances}).");
                return;
            }
        }

        // Get spawn position
        Vector3 spawnPosition = GetSpawnPosition(selectedPrefab);

        // Get an object from the pool
        GameObject npc = GetPooledObject(selectedPrefab);

        // Position and activate the NPC
        npc.transform.position = spawnPosition;
        npc.SetActive(true);

        // Ensure SoulCreatureLogic state is properly reset after positioning
        // This is a safety check in case OnEnable doesn't handle all cases
        SoulCreatureLogic soulCreature = npc.GetComponent<SoulCreatureLogic>();
        if (soulCreature != null)
        {
            // Force trail system reset after positioning to ensure particles start at correct location
            if (soulCreature.enableParticleFollowing && soulCreature.IsTrailFollowingEnabled)
            {
                soulCreature.ResetTrailSystem();
            }
        }

        // Configure NPC behavior based on prefab settings
        ConfigureNPC(npc, selectedPrefab);

        // Add to active NPCs
        activeNPCs.Add(npc);
        npcToPrefabMap[npc] = selectedPrefab;
        activeNPCCount++;

        // Always log ocean creature spawns to help diagnose issues
        if (selectedPrefab.environment == EnvironmentType.Ocean)
        {
//            Debug.Log($"Spawned ocean creature {selectedPrefab.prefabName} at position {spawnPosition}");
        }

        // Set cooldown timer if enabled for this prefab
        if (selectedPrefab.useSpawnCooldown)
        {
            prefabCooldownTimers[selectedPrefab] = selectedPrefab.spawnCooldownDuration;
            //Debug.Log($"Set cooldown for {selectedPrefab.prefabName}: {selectedPrefab.spawnCooldownDuration} seconds");
        }
    }

    // Reusable collection for prefab selection
    private List<CreaturePrefab> reusableValidPrefabs = new List<CreaturePrefab>();

    private CreaturePrefab SelectPrefabByChance()
    {
        // Get current player environment
        EnvironmentType currentPlayerEnvironment = environmentManager.GetEnvironmentState(playerTransform.position.y);

//        Debug.Log($"SelectPrefabByChance: Player is in environment: {currentPlayerEnvironment}");

        // No longer prevent spawning if player is in BelowOcean environment
        // This allows ocean creatures to spawn immediately when the game starts

        // Clear and reuse existing list to avoid allocation
        reusableValidPrefabs.Clear();

        // Include creatures from ALL environments (except BelowOcean)
        foreach (var prefab in prefabs)
        {
            // Skip BelowOcean creatures
            if (prefab.environment == EnvironmentType.BelowOcean)
                continue;

            // Check if this prefab has environment-specific spawning enabled
            if (prefab.onlySpawnInPlayerEnvironment)
            {
                // Only add if the player is in the required environment
                if (prefab.requiredPlayerEnvironment == currentPlayerEnvironment)
                {
                    reusableValidPrefabs.Add(prefab);
//                    Debug.Log($"Added environment-specific prefab: {prefab.prefabName} (required env: {prefab.requiredPlayerEnvironment})");
                }
                else
                {
//                    Debug.Log($"Skipped environment-specific prefab: {prefab.prefabName} (required env: {prefab.requiredPlayerEnvironment}, player env: {currentPlayerEnvironment})");
                }
            }
            else
            {
                // No environment restriction, add to valid prefabs
                reusableValidPrefabs.Add(prefab);
//                Debug.Log($"Added regular prefab: {prefab.prefabName} (env: {prefab.environment})");
            }
        }

        if (reusableValidPrefabs.Count == 0) return null;

        // Calculate total spawn chance
        float totalChance = 0f;
        foreach (var prefab in reusableValidPrefabs)
        {
            totalChance += prefab.spawnChance;
        }

        // If total chance is 0, return null
        if (totalChance <= 0f) return null;

        // Select a random value
        float randomValue = Random.Range(0f, totalChance);

        // Find the selected prefab
        float currentSum = 0f;
        foreach (var prefab in reusableValidPrefabs)
        {
            currentSum += prefab.spawnChance;
            if (randomValue <= currentSum)
            {
                return prefab;
            }
        }

        // Fallback
        return reusableValidPrefabs[0];
    }

    private void GetEnvironmentBounds(EnvironmentType environment, out float minY, out float maxY)
    {
        switch (environment)
        {
            case EnvironmentType.BelowOcean:
                minY = 0f; // Ground level
                maxY = environmentManager.oceanBottomY;
                break;
            case EnvironmentType.Ocean:
                minY = environmentManager.oceanBottomY;
                maxY = environmentManager.cloudsBottomY;
                break;
            case EnvironmentType.Clouds:
                minY = environmentManager.cloudsBottomY;
                maxY = environmentManager.cloudsTopY;
                break;
            case EnvironmentType.AboveClouds:
                minY = environmentManager.cloudsTopY;
                maxY = environmentManager.cloudsTopY + 50f; // Arbitrary upper limit
                break;
            default:
                minY = 0f;
                maxY = 100f;
                break;
        }
    }

    private Vector3 GetSpawnPosition(CreaturePrefab prefabConfig)
    {
        // Try multiple spawn positions and pick the one with lowest creature density
        const int maxAttempts = 5;
        Vector3[] potentialPositions = new Vector3[maxAttempts];
        float[] densityScores = new float[maxAttempts];

        // Determine which spawn distance to use
        float effectiveSpawnDistance = spawnDistance;
        if (prefabConfig.useCustomSpawnDistance)
        {
            // Randomly select a value between min and max custom spawn distances
            effectiveSpawnDistance = Random.Range(prefabConfig.minCustomSpawnDistance, prefabConfig.maxCustomSpawnDistance);
        }

        for (int i = 0; i < maxAttempts; i++)
        {
            // Get random direction on XZ plane
            Vector2 randomDirection = Random.insideUnitCircle.normalized;

            // Calculate base position using the effective spawn distance
            potentialPositions[i] = new Vector3(
                playerTransform.position.x + randomDirection.x * effectiveSpawnDistance,
                0f, // Will be set based on Y-level rules
                playerTransform.position.z + randomDirection.y * effectiveSpawnDistance
            );

            // Calculate density score for this position
            densityScores[i] = CalculateDensityScore(potentialPositions[i], prefabConfig.environment);
        }

        // Find the position with the lowest density score
        int bestIndex = 0;
        float lowestDensity = float.MaxValue;

        for (int i = 0; i < maxAttempts; i++)
        {
            if (densityScores[i] < lowestDensity)
            {
                lowestDensity = densityScores[i];
                bestIndex = i;
            }
        }

        // Use the position with the lowest density
        Vector3 basePosition = potentialPositions[bestIndex];

        // Determine Y position based on environment constraints and prefab settings
        // IMPORTANT: Use the prefab's environment type, NOT the player's current environment
        float minY, maxY;
        GetEnvironmentBounds(prefabConfig.environment, out minY, out maxY);

        // Double-check that we're using the correct environment boundaries
        // This ensures Ocean creatures spawn in Ocean, regardless of player position
        switch (prefabConfig.environment)
        {
            case EnvironmentType.Ocean:
                // Ensure we're using the ocean boundaries
                minY = environmentManager.oceanBottomY + 2f; // Add a small buffer to ensure creatures spawn above ocean bottom
                maxY = environmentManager.cloudsBottomY - 2f; // Add a small buffer to ensure creatures spawn below clouds
                break;
            case EnvironmentType.Clouds:
                // Ensure we're using the clouds boundaries
                minY = environmentManager.cloudsBottomY + 2f; // Add a small buffer to ensure creatures spawn above cloud bottom
                maxY = environmentManager.cloudsTopY - 2f; // Add a small buffer to ensure creatures spawn below cloud top
                break;
            case EnvironmentType.AboveClouds:
                // Ensure we're using the above clouds boundaries
                minY = environmentManager.cloudsTopY + 2f; // Add a small buffer to ensure creatures spawn above cloud top
                maxY = environmentManager.cloudsTopY + 50f;
                break;
        }

        // Apply Y-level spawn rules with gradient probability
        float optimalMinY = Mathf.Max(minY, prefabConfig.minOptimalY);
        float optimalMaxY = Mathf.Min(maxY, prefabConfig.maxOptimalY);

        // Ensure there's a valid range (at least 1 unit)
        if (optimalMaxY - optimalMinY < 1f)
        {
            optimalMaxY = optimalMinY + 1f;
            // Make sure we're still within environment bounds
            optimalMaxY = Mathf.Min(optimalMaxY, maxY);
        }

        // Calculate the extended range with gradient
        float extendedMinY = Mathf.Max(minY, optimalMinY - yGradientWidth);
        float extendedMaxY = Mathf.Min(maxY, optimalMaxY + yGradientWidth);

        // Determine Y position with probability gradient
        float y;
        float randomValue = Random.value;

        if (randomValue < 0.7f) // 70% chance to spawn in optimal range
        {
            // Spawn in optimal range
            y = Random.Range(optimalMinY, optimalMaxY);
        }
        else
        {
            // Spawn in gradient area with decreasing probability
            if (Random.value < 0.5f && extendedMinY < optimalMinY)
            {
                // Lower gradient area
                y = Mathf.Lerp(extendedMinY, optimalMinY, Random.value);
            }
            else if (extendedMaxY > optimalMaxY)
            {
                // Upper gradient area
                y = Mathf.Lerp(optimalMaxY, extendedMaxY, Random.value);
            }
            else
            {
                // Fallback to optimal range if gradient areas aren't available
                y = Random.Range(optimalMinY, optimalMaxY);
            }
        }

        // Final safety check to ensure Y is within environment bounds
        y = Mathf.Clamp(y, minY, maxY);

        // Set final Y position
        basePosition.y = y;

        return basePosition;
    }

    /// <summary>
    /// Counts the number of active instances of a specific prefab.
    /// </summary>
    /// <param name="prefab">The prefab to count instances of</param>
    /// <returns>The number of active instances of the specified prefab</returns>
    private int CountActiveInstancesOfPrefab(CreaturePrefab prefab)
    {
        int count = 0;
        foreach (var npc in activeNPCs)
        {
            if (npc == null) continue;

            // Check if this NPC is an instance of the specified prefab
            if (npcToPrefabMap.TryGetValue(npc, out CreaturePrefab npcPrefab) && npcPrefab == prefab)
            {
                count++;
            }
        }
        return count;
    }

    #endregion // Spawn Logic

    #region Despawn Logic

    // Reusable collection for despawn checks
    private List<GameObject> reusableNpcsToRemove = new List<GameObject>();

    private void CheckForDespawns()
    {
        // Clear and reuse existing list to avoid allocation
        reusableNpcsToRemove.Clear();

        foreach (var npc in activeNPCs)
        {
            if (npc == null)
            {
                reusableNpcsToRemove.Add(npc);
                continue;
            }

            // Get the prefab config to check for custom despawn distance
            float effectiveDespawnDistance = despawnDistance;

            if (npcToPrefabMap.TryGetValue(npc, out CreaturePrefab prefabConfig))
            {
                effectiveDespawnDistance = prefabConfig.customDespawnDistance;
            }

            // Check distance from player
            float distance = Vector3.Distance(npc.transform.position, playerTransform.position);

            if (distance > effectiveDespawnDistance)
            {
                reusableNpcsToRemove.Add(npc);
            }
        }

        // Remove NPCs that are too far away
        foreach (var npc in reusableNpcsToRemove)
        {
            ReturnToPool(npc);
        }
    }

    #endregion

    #region Density Calculation

    // Parameters for density calculation
    [Header("Density Calculation")]
    [Tooltip("Radius to check for nearby creatures when calculating density")]
    [SerializeField] private float densityCheckRadius = 15f;

    [Tooltip("Weight factor for distance in density calculation")]
    [SerializeField, Range(0.1f, 5f)] private float distanceWeight = 1.5f;

    /// <summary>
    /// Calculates a density score for a potential spawn position.
    /// Lower scores indicate better (less crowded) positions.
    /// </summary>
    private float CalculateDensityScore(Vector3 position, EnvironmentType environment)
    {
        float densityScore = 0f;

        // Get environment Y bounds
        GetEnvironmentBounds(environment, out float minY, out float maxY);

        // Set a reasonable Y position for density calculation
        position.y = (minY + maxY) / 2f;

        // Check all active NPCs
        foreach (var npc in activeNPCs)
        {
            if (npc == null) continue;

            // Get the prefab config for this NPC
            if (!npcToPrefabMap.TryGetValue(npc, out CreaturePrefab prefabConfig))
                continue;

            // Only consider NPCs in the same environment
            if (prefabConfig.environment != environment)
                continue;

            // Calculate distance (XZ plane only for better distribution)
            Vector3 npcPos = npc.transform.position;
            Vector3 posXZ = new Vector3(position.x, 0, position.z);
            Vector3 npcXZ = new Vector3(npcPos.x, 0, npcPos.z);
            float distance = Vector3.Distance(posXZ, npcXZ);

            // Only consider NPCs within the density check radius
            if (distance <= densityCheckRadius)
            {
                // Add to density score (closer NPCs contribute more)
                float contribution = 1f - (distance / densityCheckRadius);
                contribution = Mathf.Pow(contribution, distanceWeight); // Apply weight to emphasize closer creatures
                densityScore += contribution;
            }
        }

        return densityScore;
    }

    #endregion

    #region Configuration

    private void ConfigureNPC(GameObject npc, CreaturePrefab prefabConfig)
    {
        // Use custom despawn distance if specified
        float effectiveDespawnDistance = prefabConfig.useCustomDespawnDistance ? prefabConfig.customDespawnDistance : despawnDistance;

        // Set isOceanCreature flag for SoulCreatureGiant components
        SoulCreatureGiant giantCreature = npc.GetComponent<SoulCreatureGiant>();
        if (giantCreature != null && prefabConfig.environment == EnvironmentType.Ocean)
        {
            giantCreature.isOceanCreature = true;
        }
    }

    #endregion
}
