using UnityEngine;
using System.Collections.Generic;
using FMODUnity;
using System;

/// <summary>
/// Represents a flight time boost source with metadata
/// </summary>
[Serializable]
public class FlightBoostSource
{
    /// <summary>
    /// The GameObject that is the source of this boost
    /// </summary>
    public GameObject sourceObject;

    /// <summary>
    /// The amount of flight time boost provided
    /// </summary>
    public float boostAmount;

    /// <summary>
    /// Optional color for visual feedback
    /// </summary>
    public Color boostColor = Color.white;

    /// <summary>
    /// Optional tag for categorizing boost sources
    /// </summary>
    public string boostTag = "";

    /// <summary>
    /// Time when this boost was last updated
    /// </summary>
    public float lastUpdateTime;

    /// <summary>
    /// Visual feedback objects associated with this boost
    /// </summary>
    public GameObject visualFeedback;
}

/// <summary>
/// Controls player movement, gravity, and interactions with SoulCreatures.
/// </summary>
public class PlayerController : MonoBehaviour
{
    [Header("Player Settings")]
    [Tooltip("Base movement speed.")]
    public float moveSpeed = 4f;
    [Toolt<PERSON>("Base fly speed.")]
    public float flySpeed = 2f;
    [Header("Flight Boost Settings")]
    [Tooltip("Maximum total boost time the player can accumulate.")]
    public float maxTotalBoostCapacity = 20f;
    [Tooltip("Maximum number of boost sources to track")]
    public int maxBoostSources = 10;

    [Header("Flight Boost Debug")]
    [Tooltip("Enable debug logging for flight boosts")]
    public bool debugFlightBoosts = false;

    [Header("Gravity Settings")]
    [Tooltip("When enabled, the player is not affected by downward gravity")]
    public bool disableGravity = false;

    // --- Internal boost tracking ---
    private float currentTotalActualBoostTime = 0f;
    private Dictionary<GameObject, FlightBoostSource> flightBoostSources = new Dictionary<GameObject, FlightBoostSource>();

    [Header("References")]
    private Transform cameraTransform; // Now private, will get from GameManager
    public GameObject oceanBubbles;
    public GameObject passiveOceanBubbles;

    [Header("Jetfuel Particles")]
    [SerializeField] private ParticleSystem jetfuel;
    [SerializeField] private ParticleSystem jetfuel2_bubbles;
    [Header("Backpack Particles")]
    [SerializeField] private GameObject backpackParticles;
    [Header("Backpack Halo Particles")]
    [SerializeField] private GameObject backpackParticlesHalo;
    [Tooltip("Reference to the BackpackParticlesPool (optional)")]
    private BackpackParticlesPool particlesPool;
    [Header("Ocean Exit Bubbles")]
    [SerializeField] private GameObject oceanExitBubbles; // Assign your exit bubbles prefab

    // --- SoulCreature interaction system ---
    private class SoulCreatureBoost
    {
        public GameObject sourceCreature; // Reference to the creature that gave this boost
        public float currentContribution = 0f; // How much this creature is currently contributing to the total
        public float maxPossibleContribution = 0f; // The max this creature CAN contribute (its flightBoostValue)

        public GameObject backpackParticlesInstance = null;
        public ParticleSystem psInstance = null;
        public GameObject backpackParticlesHaloInstance = null;
        public ParticleSystem psHaloInstance = null;

        public bool pendingDestruction = false;
        public float destructionTimer = 0f;
        public bool haloPendingDestruction = false;
        public float haloDestructionTimer = 0f;
    }
    // map SoulCreature GameObject to SoulCreatureBoost object
    private Dictionary<GameObject, SoulCreatureBoost> soulCreatureBoosts = new();

    private ParticleSystem.MainModule jetfuelMain;
    private ParticleSystem.EmissionModule jetfuelEmission;
    private ParticleSystem.MainModule jetfuel2Main;
    private ParticleSystem.EmissionModule jetfuel2Emission;

    private CharacterController controller;
    private Vector3 velocity;
    private float flyTimer = 0f;
    private float maxFlyTime = 0.5f;

    [HideInInspector]
    public Vector3 lastMoveDirectionXZ = Vector3.zero;
    [HideInInspector]
    public Vector3 lastMoveDirection3D = Vector3.zero;

    // Environment state
    public bool isInsideOcean = false;
    public float additionalMaxFlyTime = 0.0f;

    // --- Refactored: Use EnvironmentManager for gravity/maxFlyTime ---
    private EnvironmentManager envManager;

    // Add missing fields for ocean bottom Y and ocean bubbles trigger
    private float oceanBottomY = 0.0f;
    private bool shouldOceanBubbles = false;

    private float lastY = 0f; // Track previous Y for ocean entry/exit detection

    // --- New: Accumulate external movement (e.g., from river) ---
    [HideInInspector]
    public Vector3 externalMovementThisFrame = Vector3.zero;

    // --- Flight Boost Speed Increase ---
    [Header("Flight Boost Speed Increase")]
    [Tooltip("Speed increase applied when additionalMaxFlyTime is set. This value is automatically set to match additionalMaxFlyTime at runtime, but can be set in the inspector for testing.")]
    public float flightBoostSpeedIncrease = 0f;

    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;

    // --- Reusable collections to avoid garbage allocation ---
    private List<GameObject> reusableKeysToRemove = new List<GameObject>();
    private List<GameObject> reusableKeysSnapshot = new List<GameObject>();
    private List<GameObject> reusableFlightBoostSourcesToRemove = new List<GameObject>();

    void Start()
    {
        controller = GetComponent<CharacterController>();
        envManager = EnvironmentManager.Instance;

        // Get camera reference from GameManager
        if (GameManager.Instance != null && GameManager.Instance.followCamera != null)
        {
            cameraTransform = GameManager.Instance.followCamera.transform;
        }
        else
        {
            Debug.LogError("FollowCamera reference not found in GameManager!");
        }

        // Initialize oceanBottomY from EnvironmentManager if possible
        if (envManager != null)
            oceanBottomY = envManager.oceanBottomY;

        shouldOceanBubbles = true;

        if (jetfuel != null)
        {
            jetfuelMain = jetfuel.main;
            jetfuelEmission = jetfuel.emission;
            jetfuelEmission.rateOverTime = 0f; // Start with no emission
        }
        if (jetfuel2_bubbles != null)
        {
            jetfuel2Main = jetfuel2_bubbles.main;
            jetfuel2Emission = jetfuel2_bubbles.emission;
            jetfuel2Emission.rateOverTime = 0f; // Start with no emission
        }

        lastY = transform.position.y;
    }

    void Update()
    {
        HandleSoulCreatureBoosts();
        HandleFlightBoosts();
        HandleMovement();
        UpdateJetfuelParticles();

        // Lock the player's rotation
        transform.rotation = Quaternion.Euler(0, 0, 0);

        // --- Use EnvironmentManager for environment state ---
        var env = envManager.GetEnvironmentState(transform.position.y);
        isInsideOcean = env == EnvironmentType.Ocean;

        // Ocean bubbles and passive bubbles logic
        float posY = transform.position.y;

        // --- Ocean bubbles logic ---
        // Detect entry/exit through bottom
        bool wasInsideOcean = (lastY >= oceanBottomY && lastY < envManager.cloudsBottomY);
        bool nowInsideOcean = (posY >= oceanBottomY && posY < envManager.cloudsBottomY);

        // Entering ocean from below
        if (!wasInsideOcean && nowInsideOcean && lastY < oceanBottomY && posY >= oceanBottomY)
        {
            passiveOceanBubbles.SetActive(true);
            if (shouldOceanBubbles)
            {
                var o = Instantiate(oceanBubbles, new Vector3(transform.position.x, oceanBottomY, transform.position.z), Quaternion.identity);
                shouldOceanBubbles = false;
                Destroy(o, 10f);
            }
        }
        // Exiting ocean through bottom
        else if (wasInsideOcean && !nowInsideOcean && lastY >= oceanBottomY && posY < oceanBottomY)
        {
            passiveOceanBubbles.SetActive(false);
            shouldOceanBubbles = true;
            if (oceanExitBubbles != null)
            {
                var o = Instantiate(oceanExitBubbles, new Vector3(transform.position.x, oceanBottomY, transform.position.z), Quaternion.identity);
                Destroy(o, 10f);
            }
        }
        // Entering/exiting at the top (clouds boundary): do nothing
        else if (posY >= envManager.cloudsBottomY)
        {
            passiveOceanBubbles.SetActive(false);
        }

        lastY = posY;
    }

    void HandleMovement()
    {
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);

        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");

        Vector3 camForward = cameraTransform.forward;
        Vector3 camRight = cameraTransform.right;

        camForward.y = 0;
        camRight.y = 0;

        camForward.Normalize();
        camRight.Normalize();

        Vector3 move = camForward * vertical + camRight * horizontal;

        // Apply speed increase from flightBoostSpeedIncrease
        float effectiveMoveSpeed = moveSpeed + flightBoostSpeedIncrease;

        lastMoveDirectionXZ = move * effectiveMoveSpeed;
        lastMoveDirection3D = lastMoveDirectionXZ;

        controller.Move(move * effectiveMoveSpeed * clampedDeltaTime);

        // Use environment-dependent gravity and maxFlyTime
        var env = envManager.GetEnvironmentState(transform.position.y);
        float gravity = envManager.GetGravity(env);
        maxFlyTime = envManager.GetMaxFlyTime(env);

        if (controller.isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
            flyTimer = 0f;
        }
        else
        {
            if (disableGravity)
            {
                // When gravity is disabled:
                // 1. Apply positive gravity (if any) to allow upward environmental forces
                // 2. Apply a small dampening effect to prevent drift when not actively flying
                if (gravity > 0)
                {
                    velocity.y += gravity * clampedDeltaTime;
                    Debug.Log("case9");
                }
                else if (!Input.GetKey(KeyCode.Space) && Mathf.Abs(velocity.y) < 0.1f)
                {
                    // Apply a small dampening effect to stabilize hovering
                    velocity.y = 0f;
                    Debug.Log("case8");
                }
            }
            else
            {
                // Normal gravity behavior when gravity is enabled
                velocity.y += gravity * clampedDeltaTime;
            }
        }

        float effectiveMaxFlyTime = maxFlyTime + additionalMaxFlyTime;

        // Debug flight time info
        if (Input.GetKeyDown(KeyCode.Space))
        {
            //            Debug.Log($"Flight info: flyTimer={flyTimer}, maxFlyTime={maxFlyTime}, additionalMaxFlyTime={additionalMaxFlyTime}, effectiveMaxFlyTime={effectiveMaxFlyTime}");
        }

        // Reset flyTimer to maxFlyTime when using additional flight time
        // This ensures we're using the additional time first
        if (flyTimer > maxFlyTime && additionalMaxFlyTime > 0)
        {
            flyTimer = maxFlyTime;
        }

        // Apply flying when space is pressed and we have flight time available
        if (Input.GetKey(KeyCode.Space) && flyTimer < effectiveMaxFlyTime)
        {
            // Apply upward velocity
            velocity.y = flySpeed;

            // Increment the flight timer
            flyTimer += clampedDeltaTime;

            // Update the movement direction for visuals
            lastMoveDirection3D.y = flySpeed;

            Debug.Log("case1");

            // Debug when using additional flight time
            if (flyTimer >= maxFlyTime && additionalMaxFlyTime > 0)
            {
                //                Debug.Log($"Using additional flight time: {flyTimer - maxFlyTime}/{additionalMaxFlyTime}");
            }
        }
        else
        {
            // If gravity is disabled and player just released space, gradually reduce vertical movement
            if (disableGravity && Input.GetKeyUp(KeyCode.Space))
            {
                // Apply a dampening effect to create a smoother transition to hovering
                velocity.y *= 0.3f; // Reduce velocity by 70% immediately when releasing space
                Debug.Log("case2");
            }

            // Apply additional dampening when gravity is disabled and not pressing space
            if (disableGravity && !Input.GetKey(KeyCode.Space))
            {
                // Apply continuous dampening to stabilize hovering
                velocity.y *= 0.8f; // Small continuous dampening
                Debug.Log("case3");
            }

            if (transform.position.y > 0.5f)
                lastMoveDirection3D.y = velocity.y;
        }
        controller.Move(velocity * clampedDeltaTime);

        // --- Apply external movement (e.g., from river) ---
        if (externalMovementThisFrame.sqrMagnitude > 0.00001f)
        {
            controller.Move(externalMovementThisFrame);
            externalMovementThisFrame = Vector3.zero;
        }
    }

    // --- Jetfuel particles ---
    void UpdateJetfuelParticles()
    {
        // Get environment states and max fly times
        var env = envManager.GetEnvironmentState(transform.position.y);
        float baseMaxFlyTime = envManager.GetMaxFlyTime(env);
        float effectiveMaxFlyTime = baseMaxFlyTime + additionalMaxFlyTime;

        // Calculate fuel percentages (based on their respective timers)
        float baseFuelPercentage = (baseMaxFlyTime > 0) ? 1f - (flyTimer / baseMaxFlyTime) : 0f;
        float effectiveFuelPercentage = (effectiveMaxFlyTime > 0) ? 1f - (flyTimer / effectiveMaxFlyTime) : 0f;

        // --- Jetfuel (Standard) Particles ---
        if (jetfuel != null)
        {
            // Condition: Holding Space AND flyTimer < baseMaxFlyTime AND Player Y < oceanBottomY
            bool isStandardFlyingBelowOcean = Input.GetKey(KeyCode.Space) && flyTimer < baseMaxFlyTime && transform.position.y < oceanBottomY;

            if (isStandardFlyingBelowOcean)
            {
                Debug.Log("case4");
                jetfuelEmission.rateOverTime = Mathf.Lerp(5f, 25f, baseFuelPercentage);

                // Link color/lifetime to base fuel
                Color currentColor = jetfuelMain.startColor.color; // Get base color
                currentColor.a = Mathf.Lerp(0.1f, 0.2f, baseFuelPercentage);
                jetfuelMain.startColor = currentColor;
                jetfuelMain.startLifetime = Mathf.Lerp(0.3f, 1.0f, baseFuelPercentage);
            }
            else
            {
                jetfuelEmission.rateOverTime = 0f;
            }
        }

        // --- Jetfuel2_Bubbles Particles ---
        if (jetfuel2_bubbles != null)
        {
            // Condition: Holding Space AND flyTimer < effectiveMaxFlyTime AND isInsideOcean
            bool isAnyFlyingInsideOcean = Input.GetKey(KeyCode.Space) && flyTimer < effectiveMaxFlyTime && isInsideOcean;

            if (isAnyFlyingInsideOcean)
            {
                Debug.Log("case6");
                jetfuel2Emission.rateOverTime = Mathf.Lerp(5f, 25f, effectiveFuelPercentage);

                // Link color/lifetime to effective fuel
                Color currentColor = jetfuel2Main.startColor.color; // Get base color
                currentColor.a = Mathf.Lerp(0.1f, 0.2f, effectiveFuelPercentage);
                jetfuel2Main.startColor = currentColor;
                jetfuel2Main.startLifetime = Mathf.Lerp(0.5f, 1.3f, effectiveFuelPercentage);
            }
            else
            {
                Debug.Log("case7");
                jetfuel2Emission.rateOverTime = 0f;
            }
        }
    }
    void HandleSoulCreatureBoosts()
    {
        // Check if we have any SoulCreature boosts
        if (soulCreatureBoosts.Count == 0 && currentTotalActualBoostTime <= 0)
        {
            // Don't reset additionalMaxFlyTime here, as it might come from external sources
            // We'll calculate the total at the end of this method
            return;
        }

        // Clear and reuse existing lists to avoid allocation
        reusableKeysToRemove.Clear();
        reusableKeysSnapshot.Clear();

        // --- Step 1: Identify null creatures for removal ---
        // Copy keys to reusable list to avoid issues if 'other' (the key) gets destroyed
        reusableKeysSnapshot.AddRange(soulCreatureBoosts.Keys);

        foreach (var key in reusableKeysSnapshot) // Use the snapshot
        {
            if (key == null) // The SoulCreature GameObject itself was destroyed
            {
                // Attempt to clean up particles for the orphaned boost entry
                if (soulCreatureBoosts.TryGetValue(key, out SoulCreatureBoost orphanedBoost))
                {
                    if (orphanedBoost.backpackParticlesInstance != null) Destroy(orphanedBoost.backpackParticlesInstance);
                    if (orphanedBoost.backpackParticlesHaloInstance != null) Destroy(orphanedBoost.backpackParticlesHaloInstance);
                }
                reusableKeysToRemove.Add(key); // Add null key to the list to be removed from dictionary
            }
        }

        // Remove null keys identified in Step 1 immediately
        foreach (var key in reusableKeysToRemove)
        {
            soulCreatureBoosts.Remove(key);
        }
        reusableKeysToRemove.Clear(); // Clear the list to reuse it for decay/zero-contribution checks

        // Re-create snapshot after removing null keys in Step 1
        reusableKeysSnapshot.Clear();
        reusableKeysSnapshot.AddRange(soulCreatureBoosts.Keys);


        // --- Step 2: Apply Fixed Decay Per Creature and identify entries with null source ---
        float individualDecayRate = 1.0f; // Adjust this rate as needed (decay units per second per creature)

        foreach (var key in reusableKeysSnapshot) // Use the snapshot from Step 1
        {
            // Check if key or source creature is null (might happen if creature was destroyed between snapshots)
            if (key == null || !soulCreatureBoosts.TryGetValue(key, out var boost) || boost.sourceCreature == null)
            {
                if (soulCreatureBoosts.ContainsKey(key)) reusableKeysToRemove.Add(key); // Add to reusableKeysToRemove list
                continue;
            }

            // Apply fixed decay
            if (boost.currentContribution > 0f)
            {
                float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
                boost.currentContribution -= individualDecayRate * clampedDeltaTime;
                boost.currentContribution = Mathf.Max(0f, boost.currentContribution); // Clamp at zero
            }
        }
        // reusableKeysToRemove now contains entries where the source creature was destroyed.
        // This list will be used in the final removal step.


        // --- Step 3: Recalculate total, manage particles, and flag for removal if contribution is zero ---
        currentTotalActualBoostTime = 0f;
        // Use the reusableKeysSnapshot from Step 1 for this loop as well

        foreach (var key in reusableKeysSnapshot)
        {
            // Check validity again, might have been added to keysToRemove in Step 2
            if (key == null || !soulCreatureBoosts.TryGetValue(key, out var boost) || boost.sourceCreature == null)
            {
                // If already marked in Step 2, it's in keysToRemove. Skip.
                continue;
            }

            currentTotalActualBoostTime += boost.currentContribution;
            float individualBoostRatio = boost.maxPossibleContribution > 0 ?
                                        boost.currentContribution / boost.maxPossibleContribution : 0f;

            // BackpackParticles
            if (boost.backpackParticlesInstance != null && boost.psInstance != null)
            {
                float emissionRate = 0f;
                if (!boost.pendingDestruction)
                {
                    emissionRate = Mathf.Lerp(0f, 10f, individualBoostRatio);
                }

                // Use the pool to update emission rate if available
                if (particlesPool != null)
                {
                    particlesPool.UpdateEmissionRate(boost.backpackParticlesInstance, emissionRate);
                }
                else
                {
                    var emission = boost.psInstance.emission;
                    emission.rateOverTime = emissionRate;
                }

                if (boost.currentContribution <= 0f && !boost.pendingDestruction)
                {
                    boost.pendingDestruction = true;
                    boost.destructionTimer = boost.psInstance.main.startLifetime.constantMax;

                    // Set emission rate to 0
                    if (particlesPool != null)
                    {
                        particlesPool.UpdateEmissionRate(boost.backpackParticlesInstance, 0f);
                    }
                    else
                    {
                        var emission = boost.psInstance.emission;
                        emission.rateOverTime = 0f;
                    }
                }
                else if (boost.pendingDestruction)
                {
                    float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
                    boost.destructionTimer -= clampedDeltaTime;
                    if (boost.destructionTimer <= 0f)
                    {
                        // Release to pool if available
                        if (particlesPool != null)
                        {
                            particlesPool.ReleaseParticleSystem(boost.backpackParticlesInstance);
                        }
                        else
                        {
                            Destroy(boost.backpackParticlesInstance);
                        }
                        boost.backpackParticlesInstance = null;
                        boost.psInstance = null;
                    }
                }
            }

            // BackpackParticlesHalo
            if (boost.backpackParticlesHaloInstance != null && boost.psHaloInstance != null)
            {
                float haloEmissionRate = 0f;
                if (!boost.haloPendingDestruction)
                {
                    haloEmissionRate = Mathf.Lerp(0f, 10f, individualBoostRatio);
                }

                // Use the pool to update emission rate if available
                if (particlesPool != null)
                {
                    particlesPool.UpdateEmissionRate(boost.backpackParticlesHaloInstance, haloEmissionRate);
                }
                else
                {
                    var haloEmission = boost.psHaloInstance.emission;
                    haloEmission.rateOverTime = haloEmissionRate;
                }

                if (boost.currentContribution <= 0f && !boost.haloPendingDestruction)
                {
                    boost.haloPendingDestruction = true;
                    boost.haloDestructionTimer = boost.psHaloInstance.main.startLifetime.constantMax;

                    // Set emission rate to 0
                    if (particlesPool != null)
                    {
                        particlesPool.UpdateEmissionRate(boost.backpackParticlesHaloInstance, 0f);
                    }
                    else
                    {
                        var haloEmission = boost.psHaloInstance.emission;
                        haloEmission.rateOverTime = 0f;
                    }
                }
                else if (boost.haloPendingDestruction)
                {
                    boost.haloDestructionTimer -= Time.deltaTime;
                    if (boost.haloDestructionTimer <= 0f)
                    {
                        // Release to pool if available
                        if (particlesPool != null)
                        {
                            particlesPool.ReleaseParticleSystem(boost.backpackParticlesHaloInstance);
                        }
                        else
                        {
                            Destroy(boost.backpackParticlesHaloInstance);
                        }
                        boost.backpackParticlesHaloInstance = null;
                        boost.psHaloInstance = null;
                    }
                }
            }

            // If contribution is zero and particles are destroyed, mark for removal
            if (boost.currentContribution <= 0f &&
                boost.backpackParticlesInstance == null &&
                boost.backpackParticlesHaloInstance == null)
            {
                // Ensure it's not already marked for removal
                if (!reusableKeysToRemove.Contains(key))
                {
                    reusableKeysToRemove.Add(key);
                }
            }
        }

        // Calculate total external boost
        float totalExternalBoost = 0f;
        foreach (var boost in flightBoostSources.Values)
        {
            totalExternalBoost += boost.boostAmount;
        }

        // Combine SoulCreature boosts with external boosts
        additionalMaxFlyTime = Mathf.Max(0f, currentTotalActualBoostTime + totalExternalBoost);
        // Set speed increase to match additionalMaxFlyTime
        flightBoostSpeedIncrease = additionalMaxFlyTime;

        // --- Final Step: Remove entries marked for removal ---
        foreach (var keyToRemove in reusableKeysToRemove)
        {
            if (soulCreatureBoosts.ContainsKey(keyToRemove))
            {
                // Optional: Clean up particles one last time just in case
                if (soulCreatureBoosts.TryGetValue(keyToRemove, out SoulCreatureBoost boostToRemove))
                {
                    if (boostToRemove.backpackParticlesInstance != null) Destroy(boostToRemove.backpackParticlesInstance);
                    if (boostToRemove.backpackParticlesHaloInstance != null) Destroy(boostToRemove.backpackParticlesHaloInstance);
                }
                soulCreatureBoosts.Remove(keyToRemove);
            }
        }
    }
    // --- SoulCreature particle collision handler ---
    void OnParticleCollision(GameObject other)
    {
        if (!other.CompareTag("SoulCreature")) return;

        float creatureBoostValue = 0.0f;
        var boostProvider = other.GetComponent<ISoulCreatureBoostProvider>();
        if (boostProvider != null)
            creatureBoostValue = boostProvider.GetFlightBoostValue();
        else
        {
            var scFallback = other.GetComponent<SoulCreatureLogic>();
            if (scFallback != null)
                creatureBoostValue = scFallback.flightBoostValue;
        }

        SoulCreatureBoost boostEntry;
        float contributionChange = 0f;

        if (!soulCreatureBoosts.TryGetValue(other, out boostEntry))
        {
            boostEntry = new SoulCreatureBoost();
            boostEntry.sourceCreature = other;
            boostEntry.maxPossibleContribution = creatureBoostValue;
            boostEntry.currentContribution = 0f; // Start at 0, will increment below
            soulCreatureBoosts[other] = boostEntry;

            // Get the color from the soul creature's particle system
            Color soulColor = Color.white;
            var soulPs = other.GetComponentInChildren<ParticleSystem>();
            if (soulPs != null)
            {
                soulColor = soulPs.main.startColor.color;
            }

            // Get the sound event from the soul creature
            EventReference soundEvent = new EventReference();

            // For SoulCreatureTutorial, get sound2 directly
            var scTutorial = other.GetComponent<SoulCreatureTutorial>();
            if (scTutorial != null)
            {
                soundEvent = scTutorial.sound2;
            }
            else
            {
                // For all other cases, try to get sound2 from SoulCreatureAudio
                var scAudio = other.GetComponent<SoulCreatureAudio>();
                if (scAudio != null)
                {
                    soundEvent = scAudio.Sound2;
                }

                // --- NEW: Check if this soul creature disables backpack particles ---
                if (scAudio != null && scAudio.disableBackpackParticles)
                {
                    // Do not spawn any backpack particles for this creature
                    return;
                }
            }

            // Use the BackpackParticlesPool if available
            if (particlesPool != null)
            {
                // for BackpackParticlesHalo:
                if (boostEntry.backpackParticlesHaloInstance == null)
                {
                    boostEntry.backpackParticlesHaloInstance = particlesPool.GetBackpackHalo(soulColor, other);
                    if (boostEntry.backpackParticlesHaloInstance != null)
                    {
                        boostEntry.psHaloInstance = boostEntry.backpackParticlesHaloInstance.GetComponent<ParticleSystem>();
                    }
                }

                // for BackpackParticles:
                if (boostEntry.backpackParticlesInstance == null)
                {
                    boostEntry.backpackParticlesInstance = particlesPool.GetBackpackParticles(soulColor, soundEvent, other);
                    if (boostEntry.backpackParticlesInstance != null)
                    {
                        boostEntry.psInstance = boostEntry.backpackParticlesInstance.GetComponent<ParticleSystem>();
                    }
                }
            }
            // Fall back to the old instantiation method if pool is not available
            else
            {
                // for BackpackParticlesHalo:
                if (backpackParticlesHalo != null && boostEntry.backpackParticlesHaloInstance == null)
                {
                    boostEntry.backpackParticlesHaloInstance = Instantiate(backpackParticlesHalo, backpackParticlesHalo.transform.parent);
                    boostEntry.backpackParticlesHaloInstance.SetActive(true);
                    boostEntry.psHaloInstance = boostEntry.backpackParticlesHaloInstance.GetComponent<ParticleSystem>();

                    if (soulPs != null && boostEntry.psHaloInstance != null)
                    {
                        var mainHalo = boostEntry.psHaloInstance.main;
                        mainHalo.startColor = soulColor;
                    }
                }

                // for BackpackParticles:
                if (backpackParticles != null && boostEntry.backpackParticlesInstance == null)
                {
                    boostEntry.backpackParticlesInstance = Instantiate(backpackParticles, backpackParticles.transform.parent);
                    boostEntry.backpackParticlesInstance.SetActive(true);
                    boostEntry.psInstance = boostEntry.backpackParticlesInstance.GetComponent<ParticleSystem>();

                    // Add sound2 to the backpack particles
                    var soundOnDeath = boostEntry.backpackParticlesInstance.GetComponent<BackpackParticlesSoundOnDeath>();
                    if (soundOnDeath == null) soundOnDeath = boostEntry.backpackParticlesInstance.AddComponent<BackpackParticlesSoundOnDeath>();
                    soundOnDeath.sound2 = soundEvent;

                    if (soulPs != null && boostEntry.psInstance != null)
                    {
                        var mainParticles = boostEntry.psInstance.main;
                        mainParticles.startColor = soulColor;
                    }
                }
            }
        }

        // Increment the boost per collision, but do not exceed maxPossibleContribution
        float neededToMax = boostEntry.maxPossibleContribution - boostEntry.currentContribution;
        contributionChange = Mathf.Min(0.8f, Mathf.Max(0, neededToMax));
        boostEntry.currentContribution += contributionChange;

        currentTotalActualBoostTime += contributionChange;
        // Use the declared class member: maxTotalBoostCapacity
        currentTotalActualBoostTime = Mathf.Min(currentTotalActualBoostTime, maxTotalBoostCapacity);
        currentTotalActualBoostTime = Mathf.Max(0, currentTotalActualBoostTime);

        boostEntry.pendingDestruction = false;
        boostEntry.destructionTimer = 0f;
        boostEntry.haloPendingDestruction = false;
        boostEntry.haloDestructionTimer = 0f;

        if (boostEntry.psInstance != null)
        {
            var tempPsInstanceEmission = boostEntry.psInstance.emission;
            tempPsInstanceEmission.enabled = true;
        }
        if (boostEntry.psHaloInstance != null)
        {
            var tempPsHaloEmission = boostEntry.psHaloInstance.emission;
            tempPsHaloEmission.enabled = true;
        }

        // Also add this boost to the new flight boost system for visualization and debugging
        // This doesn't affect the actual boost amount, just provides visual feedback
        if (debugFlightBoosts)
        {
            // Get the color from the soul creature's particle system
            Color visualColor = Color.white;
            var soulPs = other.GetComponentInChildren<ParticleSystem>();
            if (soulPs != null)
            {
                visualColor = soulPs.main.startColor.color;
            }

            AddFlightTimeBoost(other, boostEntry.currentContribution, visualColor, "SoulCreature");
        }
    }

    /// <summary>
    /// Handles the management of flight boosts including decay and cleanup
    /// </summary>
    private void HandleFlightBoosts()
    {
        if (flightBoostSources.Count == 0)
            return;

        // Clear and reuse existing list to avoid allocation
        reusableFlightBoostSourcesToRemove.Clear();

        // Check for null sources and apply decay
        foreach (var kvp in flightBoostSources)
        {
            GameObject source = kvp.Key;

            // Check for null sources
            if (source == null)
            {
                reusableFlightBoostSourcesToRemove.Add(source);
                continue;
            }

            // Check for destroyed sources
            if (!source.activeInHierarchy && source.scene.rootCount == 0)
            {
                reusableFlightBoostSourcesToRemove.Add(source);
                continue;
            }
        }

        // Remove any null or destroyed sources
        foreach (var source in reusableFlightBoostSourcesToRemove)
        {
            RemoveFlightTimeBoost(source);
        }

        // Recalculate total boost (already done in RemoveFlightTimeBoost)
        if (reusableFlightBoostSourcesToRemove.Count > 0)
        {
            if (debugFlightBoosts)
                Debug.Log($"Removed {reusableFlightBoostSourcesToRemove.Count} invalid flight boost sources");
        }
    }

    #region Flight Boost System

    /// <summary>
    /// Adds or updates a flight time boost from the specified source
    /// </summary>
    /// <param name="source">The GameObject providing the boost</param>
    /// <param name="amount">The amount of flight time to add</param>
    /// <returns>True if the boost was successfully added or updated</returns>
    public bool AddFlightTimeBoost(GameObject source, float amount)
    {
        return AddFlightTimeBoost(source, amount, Color.white, "");
    }

    /// <summary>
    /// Adds or updates a flight time boost with additional metadata
    /// </summary>
    /// <param name="source">The GameObject providing the boost</param>
    /// <param name="amount">The amount of flight time to add</param>
    /// <param name="color">Color for visual feedback</param>
    /// <param name="tag">Optional tag for categorizing the boost</param>
    /// <returns>True if the boost was successfully added or updated</returns>
    public bool AddFlightTimeBoost(GameObject source, float amount, Color color, string tag = "")
    {
        if (source == null)
        {
            if (debugFlightBoosts)
                Debug.LogWarning("Flight boost source cannot be null");
            return false;
        }

        // Enforce maximum boost sources limit
        if (!flightBoostSources.ContainsKey(source) && flightBoostSources.Count >= maxBoostSources)
        {
            if (debugFlightBoosts)
                Debug.LogWarning($"Maximum number of boost sources ({maxBoostSources}) reached. Cannot add new source.");
            return false;
        }

        // Add or update the boost source
        if (flightBoostSources.TryGetValue(source, out var existingBoost))
        {
            existingBoost.boostAmount = amount;
            existingBoost.boostColor = color;
            existingBoost.boostTag = tag;
            existingBoost.lastUpdateTime = Time.time;
            flightBoostSources[source] = existingBoost;

            if (debugFlightBoosts)
                Debug.Log($"Updated flight boost from {source.name}: {amount:F2}s");
        }
        else
        {
            flightBoostSources[source] = new FlightBoostSource
            {
                sourceObject = source,
                boostAmount = amount,
                boostColor = color,
                boostTag = tag,
                lastUpdateTime = Time.time
            };

            if (debugFlightBoosts)
                Debug.Log($"Added new flight boost from {source.name}: {amount:F2}s");
        }

        // Recalculate total boost
        RecalculateTotalBoost();
        return true;
    }

    /// <summary>
    /// Removes a flight time boost from the specified source
    /// </summary>
    /// <param name="source">The GameObject that provided the boost</param>
    /// <returns>True if the boost was successfully removed</returns>
    public bool RemoveFlightTimeBoost(GameObject source)
    {
        if (source == null || !flightBoostSources.ContainsKey(source))
            return false;

        // Clean up any visual feedback
        var boost = flightBoostSources[source];
        if (boost.visualFeedback != null)
            Destroy(boost.visualFeedback);

        flightBoostSources.Remove(source);

        if (debugFlightBoosts)
            Debug.Log($"Removed flight boost from {source.name}");

        // Recalculate total boost
        RecalculateTotalBoost();
        return true;
    }

    /// <summary>
    /// Gets the current flight boost amount from a specific source
    /// </summary>
    /// <param name="source">The source GameObject</param>
    /// <returns>The current boost amount, or 0 if the source is not found</returns>
    public float GetFlightBoostAmount(GameObject source)
    {
        if (source == null || !flightBoostSources.ContainsKey(source))
            return 0f;

        return flightBoostSources[source].boostAmount;
    }

    /// <summary>
    /// Gets all active flight boost sources
    /// </summary>
    /// <returns>A list of all active flight boost sources</returns>
    public List<FlightBoostSource> GetAllFlightBoosts()
    {
        return new List<FlightBoostSource>(flightBoostSources.Values);
    }

    /// <summary>
    /// Recalculates the total boost from all sources
    /// </summary>
    private void RecalculateTotalBoost()
    {
        float totalExternalBoost = 0f;
        foreach (var boost in flightBoostSources.Values)
        {
            totalExternalBoost += boost.boostAmount;
        }

        // Combine SoulCreature boosts with external boosts
        additionalMaxFlyTime = Mathf.Max(0f, currentTotalActualBoostTime + totalExternalBoost);
        // Set speed increase to match additionalMaxFlyTime
        flightBoostSpeedIncrease = additionalMaxFlyTime;
    }

    /// <summary>
    /// Clears all flight boosts
    /// </summary>
    public void ClearAllFlightBoosts()
    {
        // Clean up any visual feedback
        foreach (var boost in flightBoostSources.Values)
        {
            if (boost.visualFeedback != null)
                Destroy(boost.visualFeedback);
        }

        flightBoostSources.Clear();
        RecalculateTotalBoost();

        if (debugFlightBoosts)
            Debug.Log("Cleared all flight boosts");
    }

    // Legacy support for existing code
    [Obsolete("Use AddFlightTimeBoost instead")]
    public void UpdateExternalFlightBoost(GameObject source, float boostAmount)
    {
        AddFlightTimeBoost(source, boostAmount);
    }

    // Legacy support for existing code
    [Obsolete("Use RemoveFlightTimeBoost instead")]
    public void RemoveExternalFlightBoost(GameObject source)
    {
        RemoveFlightTimeBoost(source);
    }

    #endregion

    /// <summary>
    /// Toggles whether gravity affects the player in the downward direction.
    /// </summary>
    /// <param name="disable">When true, player is not affected by downward gravity</param>
    public void SetGravityDisabled(bool disable)
    {
        disableGravity = disable;
    }

}