using UnityEngine;
using System.Collections;

public class LevelDirector : MonoBeh<PERSON>our
{
    // The GameObject PREFAB to instantiate
    public GameObject soulCreature1; // Changed from objectToActivate

    // Time to wait before activating the object (in seconds)
    public float activationDelay = 20f;

    // Distance from player to position the object (at fog boundary)
    public float distanceFromPlayer = 20f;

    // Maximum attempts to find a position (might not be needed with this approach unless you add more complex positioning logic)
    public int maxPositioningAttempts = 10;

    // Reference to player and main camera
    private Transform playerTransform;
    private Camera mainCamera;

    void Awake()
    {
        // Find player (assuming it has "Player" tag)
        playerTransform = GameObject.FindGameObjectWithTag("Player").transform;

        // Get main camera
        mainCamera = Camera.main;
    }

    void Start()
    {
        // Only start the instantiation process if this component is enabled
        if (enabled)
        {
            // Start the activation/instantiation process
            StartCoroutine(InstantiateObjectAfterDelay());
        }

        // Note: objectPrefab should be assigned in the inspector and should be the inactive prefab from your project
    }

    IEnumerator InstantiateObjectAfterDelay()
    {
        // Wait for specified delay
        yield return new WaitForSeconds(activationDelay);

        if (soulCreature1 != null && playerTransform != null)
        {
            // Get random direction on XZ plane
            Vector2 randomDirection = Random.insideUnitCircle.normalized;

            // Calculate the new position relative to the player
            Vector3 newPosition = new Vector3(
                playerTransform.position.x + randomDirection.x * distanceFromPlayer,
                playerTransform.position.y, // You might want to get the original Y from the prefab or set a specific Y
                playerTransform.position.z + randomDirection.y * distanceFromPlayer
            );

            // Instantiate the object directly at the calculated position and rotation (Quaternion.identity = no rotation)
            Instantiate(soulCreature1, newPosition, Quaternion.identity);

//            Debug.Log("Object instantiated at position: " + newPosition);
        }
        else
        {
            if (soulCreature1 == null) Debug.LogError("Object Prefab not assigned in LevelDirector");
            if (playerTransform == null) Debug.LogError("Player not found (tag 'Player') in LevelDirector");
        }
    }
}